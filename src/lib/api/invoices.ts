// Invoices API - Invoice Management Functionality
// TODO: API Integration - Replace local storage with actual API endpoints
// Expected request format: { invoice: InvoiceData }
// Expected response format: { success: boolean, data: Invoice, message?: string }

export interface Invoice {
  id: string;
  invoiceNumber: string;
  customerId: string;
  customerName?: string; // Denormalized for display
  customerEmail?: string;
  customerAddress?: InvoiceAddress;
  issueDate: string;
  dueDate: string;
  status: InvoiceStatus;
  lineItems: InvoiceLineItem[];
  customHeaderFields: CustomHeaderField[];
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  balanceAmount: number;
  notes?: string;
  terms?: string;
  templateId?: string;
  createdAt: string;
  updatedAt: string;
  paidAt?: string;
  sentAt?: string;
}

export interface InvoiceLineItem {
  id: string;
  productId?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  taxRate: number;
  taxAmount: number;
  lineTotal: number;
  additionalInfo?: string;
  relatedJobId?: string;
  relatedQuoteId?: string;
}

export interface InvoiceAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface CustomHeaderField {
  id: string;
  label: string;
  value: string;
  type: 'text' | 'date' | 'number';
}

export interface InvoiceStatus {
  id: string;
  name: 'Draft' | 'Sent' | 'Paid' | 'Overdue' | 'Cancelled';
  color: string;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  taxRate: number;
  category?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceTemplate {
  id: string;
  name: string;
  logoUrl?: string;
  colorScheme: {
    primary: string;
    secondary: string;
    accent: string;
  };
  sections: TemplateSection[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateSection {
  id: string;
  type: 'header' | 'footer' | 'terms' | 'notes' | 'custom';
  content: string;
  order: number;
  isVisible: boolean;
  name?: string;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold' | 'lighter';
  textAlign?: 'left' | 'center' | 'right';
}

const INVOICES_STORAGE_KEY = 'ejp_invoices';
const PRODUCTS_STORAGE_KEY = 'ejp_products';
const INVOICE_TEMPLATES_STORAGE_KEY = 'ejp_invoice_templates';

// Default invoice statuses
const DEFAULT_INVOICE_STATUSES: InvoiceStatus[] = [
  { id: '1', name: 'Draft', color: '#6B7280' },
  { id: '2', name: 'Sent', color: '#3B82F6' },
  { id: '3', name: 'Paid', color: '#10B981' },
  { id: '4', name: 'Overdue', color: '#EF4444' },
  { id: '5', name: 'Cancelled', color: '#6B7280' }
];

// Helper functions
function getInvoicesFromStorage(): Invoice[] {
  const stored = localStorage.getItem(INVOICES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveInvoicesToStorage(invoices: Invoice[]): void {
  localStorage.setItem(INVOICES_STORAGE_KEY, JSON.stringify(invoices));
}

function getProductsFromStorage(): Product[] {
  const stored = localStorage.getItem(PRODUCTS_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveProductsToStorage(products: Product[]): void {
  localStorage.setItem(PRODUCTS_STORAGE_KEY, JSON.stringify(products));
}

function getInvoiceTemplatesFromStorage(): InvoiceTemplate[] {
  const stored = localStorage.getItem(INVOICE_TEMPLATES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveInvoiceTemplatesToStorage(templates: InvoiceTemplate[]): void {
  localStorage.setItem(INVOICE_TEMPLATES_STORAGE_KEY, JSON.stringify(templates));
}

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function generateInvoiceNumber(): string {
  const invoices = getInvoicesFromStorage();
  const year = new Date().getFullYear();
  const count = invoices.filter(inv => inv.invoiceNumber.startsWith(`INV-${year}`)).length + 1;
  return `INV-${year}-${count.toString().padStart(4, '0')}`;
}

// Invoice CRUD operations
export async function getInvoices(): Promise<Invoice[]> {
  // TODO: API Integration - GET /api/invoices
  // Expected response: { success: boolean, data: Invoice[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getInvoicesFromStorage());
    }, 100);
  });
}

export async function getInvoiceById(id: string): Promise<Invoice | null> {
  // TODO: API Integration - GET /api/invoices/{id}
  // Expected response: { success: boolean, data: Invoice }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const invoices = getInvoicesFromStorage();
      const invoice = invoices.find(inv => inv.id === id) || null;
      resolve(invoice);
    }, 100);
  });
}

export async function getInvoicesByCustomer(customerId: string): Promise<Invoice[]> {
  // TODO: API Integration - GET /api/invoices?customerId={customerId}
  // Expected response: { success: boolean, data: Invoice[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const invoices = getInvoicesFromStorage();
      const customerInvoices = invoices.filter(inv => inv.customerId === customerId);
      resolve(customerInvoices);
    }, 100);
  });
}

export async function createInvoice(invoiceData: Omit<Invoice, 'id' | 'invoiceNumber' | 'createdAt' | 'updatedAt'>): Promise<Invoice> {
  // TODO: API Integration - POST /api/invoices
  // Expected request: { invoice: InvoiceData }
  // Expected response: { success: boolean, data: Invoice }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const invoices = getInvoicesFromStorage();
      const newInvoice: Invoice = {
        ...invoiceData,
        id: generateId(),
        invoiceNumber: generateInvoiceNumber(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      invoices.push(newInvoice);
      saveInvoicesToStorage(invoices);
      resolve(newInvoice);
    }, 100);
  });
}

export async function updateInvoice(id: string, updates: Partial<Invoice>): Promise<Invoice> {
  // TODO: API Integration - PUT /api/invoices/{id}
  // Expected request: { invoice: Partial<Invoice> }
  // Expected response: { success: boolean, data: Invoice }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const invoices = getInvoicesFromStorage();
      const index = invoices.findIndex(inv => inv.id === id);
      
      if (index === -1) {
        reject(new Error('Invoice not found'));
        return;
      }
      
      invoices[index] = {
        ...invoices[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      saveInvoicesToStorage(invoices);
      resolve(invoices[index]);
    }, 100);
  });
}

export async function deleteInvoice(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/invoices/{id}
  // Expected response: { success: boolean }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const invoices = getInvoicesFromStorage();
      const index = invoices.findIndex(inv => inv.id === id);
      
      if (index === -1) {
        reject(new Error('Invoice not found'));
        return;
      }
      
      invoices.splice(index, 1);
      saveInvoicesToStorage(invoices);
      resolve();
    }, 100);
  });
}

// Initialize sample data if none exists
async function initializeSampleProducts() {
  const existingProducts = getProductsFromStorage();
  if (existingProducts.length === 0) {
    const sampleProducts: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'Plumbing Service - Basic',
        description: 'Basic plumbing service including inspection and minor repairs',
        price: 150.00,
        taxRate: 10,
        category: 'Plumbing',
        isActive: true
      },
      {
        name: 'Electrical Installation',
        description: 'Standard electrical installation and wiring',
        price: 200.00,
        taxRate: 10,
        category: 'Electrical',
        isActive: true
      },
      {
        name: 'HVAC Maintenance',
        description: 'Heating, ventilation, and air conditioning maintenance service',
        price: 180.00,
        taxRate: 10,
        category: 'HVAC',
        isActive: true
      },
      {
        name: 'General Labor',
        description: 'General labor and maintenance work',
        price: 75.00,
        taxRate: 10,
        category: 'General',
        isActive: true
      },
      {
        name: 'Emergency Call-out',
        description: 'Emergency service call-out fee',
        price: 100.00,
        taxRate: 10,
        category: 'Emergency',
        isActive: true
      }
    ];

    const products = getProductsFromStorage();
    for (const productData of sampleProducts) {
      const newProduct: Product = {
        ...productData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      products.push(newProduct);
    }
    saveProductsToStorage(products);
  }
}

async function initializeSampleTemplates() {
  const existingTemplates = getInvoiceTemplatesFromStorage();
  if (existingTemplates.length === 0) {
    const sampleTemplates: Omit<InvoiceTemplate, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'Default Template',
        colorScheme: {
          primary: '#3B82F6',
          secondary: '#6B7280',
          accent: '#10B981'
        },
        sections: [
          {
            id: generateId(),
            type: 'header',
            content: 'Easy Job Planner Invoice',
            order: 1,
            isVisible: true
          },
          {
            id: generateId(),
            type: 'terms',
            content: 'Payment due within 30 days. Please make payment via bank transfer.',
            order: 2,
            isVisible: true
          },
          {
            id: generateId(),
            type: 'footer',
            content: 'Thank you for your business!',
            order: 3,
            isVisible: true
          }
        ],
        isDefault: true
      },
      {
        name: 'Professional Template',
        colorScheme: {
          primary: '#1F2937',
          secondary: '#374151',
          accent: '#059669'
        },
        sections: [
          {
            id: generateId(),
            type: 'header',
            content: 'Professional Services Invoice',
            order: 1,
            isVisible: true
          },
          {
            id: generateId(),
            type: 'terms',
            content: 'Payment terms: Net 30 days. Late payments subject to 1.5% monthly service charge.',
            order: 2,
            isVisible: true
          }
        ],
        isDefault: false
      }
    ];

    const templates = getInvoiceTemplatesFromStorage();
    for (const templateData of sampleTemplates) {
      const newTemplate: InvoiceTemplate = {
        ...templateData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      templates.push(newTemplate);
    }
    saveInvoiceTemplatesToStorage(templates);
  }
}

// Product management
export async function getProducts(): Promise<Product[]> {
  // TODO: API Integration - GET /api/products
  // Expected response: { success: boolean, data: Product[] }
  
  return new Promise(async (resolve) => {
    setTimeout(async () => {
      await initializeSampleProducts();
      resolve(getProductsFromStorage());
    }, 100);
  });
}

export async function createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {
  // TODO: API Integration - POST /api/products
  // Expected request: { product: ProductData }
  // Expected response: { success: boolean, data: Product }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const products = getProductsFromStorage();
      const newProduct: Product = {
        ...productData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      products.push(newProduct);
      saveProductsToStorage(products);
      resolve(newProduct);
    }, 100);
  });
}

export async function updateProduct(id: string, updates: Partial<Product>): Promise<Product> {
  // TODO: API Integration - PUT /api/products/{id}
  // Expected request: { product: Partial<Product> }
  // Expected response: { success: boolean, data: Product }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const products = getProductsFromStorage();
      const index = products.findIndex(p => p.id === id);
      
      if (index === -1) {
        reject(new Error('Product not found'));
        return;
      }
      
      products[index] = {
        ...products[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      saveProductsToStorage(products);
      resolve(products[index]);
    }, 100);
  });
}

// Invoice template management
export async function getInvoiceTemplates(): Promise<InvoiceTemplate[]> {
  // TODO: API Integration - GET /api/invoice-templates
  // Expected response: { success: boolean, data: InvoiceTemplate[] }
  
  return new Promise(async (resolve) => {
    setTimeout(async () => {
      await initializeSampleTemplates();
      resolve(getInvoiceTemplatesFromStorage());
    }, 100);
  });
}

export async function createInvoiceTemplate(templateData: Omit<InvoiceTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<InvoiceTemplate> {
  // TODO: API Integration - POST /api/invoice-templates
  // Expected request: { template: InvoiceTemplateData }
  // Expected response: { success: boolean, data: InvoiceTemplate }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const templates = getInvoiceTemplatesFromStorage();
      const newTemplate: InvoiceTemplate = {
        ...templateData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      templates.push(newTemplate);
      saveInvoiceTemplatesToStorage(templates);
      resolve(newTemplate);
    }, 100);
  });
}

export async function updateInvoiceTemplate(id: string, updates: Partial<InvoiceTemplate>): Promise<InvoiceTemplate> {
  // TODO: API Integration - PUT /api/invoice-templates/{id}
  // Expected request: { template: Partial<InvoiceTemplate> }
  // Expected response: { success: boolean, data: InvoiceTemplate }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const templates = getInvoiceTemplatesFromStorage();
      const index = templates.findIndex(t => t.id === id);
      
      if (index === -1) {
        reject(new Error('Template not found'));
        return;
      }
      
      templates[index] = {
        ...templates[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      saveInvoiceTemplatesToStorage(templates);
      resolve(templates[index]);
    }, 100);
  });
}

// Calculate invoice totals
export function calculateInvoiceTotals(lineItems: InvoiceLineItem[], discountAmount: number = 0): {
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  balanceAmount: number;
} {
  const subtotal = lineItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const taxAmount = lineItems.reduce((sum, item) => sum + item.taxAmount, 0);
  const totalAmount = subtotal + taxAmount - discountAmount;
  
  return {
    subtotal,
    taxAmount,
    totalAmount,
    balanceAmount: totalAmount // Will be updated when payments are applied
  };
}

export async function deleteInvoiceTemplate(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/invoice-templates/{id}
  // Expected response: { success: boolean }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const templates = getInvoiceTemplatesFromStorage();
      const index = templates.findIndex(t => t.id === id);
      
      if (index === -1) {
        reject(new Error('Template not found'));
        return;
      }
      
      templates.splice(index, 1);
      saveInvoiceTemplatesToStorage(templates);
      resolve();
    }, 100);
  });
}

// Get invoice statuses
export async function getInvoiceStatuses(): Promise<InvoiceStatus[]> {
  // TODO: API Integration - GET /api/invoice-statuses
  // Expected response: { success: boolean, data: InvoiceStatus[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(DEFAULT_INVOICE_STATUSES);
    }, 100);
  });
} 