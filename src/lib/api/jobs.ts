// Jobs API - Job Management Functionality
// TODO: API Integration - Replace local storage with actual API endpoints
// Expected request format: { job: JobData }
// Expected response format: { success: boolean, data: Job, message?: string }

export interface Job {
  id: string;
  title: string;
  customerId: string;
  customerName?: string; // Denormalized for display
  jobType: string;
  status: JobStatus;
  description: string;
  scheduledDateTime?: string;
  assignedStaff: AssignedStaff[];
  jobAddress: JobAddress;
  customFields: CustomField[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  estimatedDuration?: number; // in minutes
  actualDuration?: number; // in minutes
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  tags: string[];
  attachments: JobAttachment[];
  notes: string;
}

export interface JobStatus {
  id: string;
  name: string;
  color: string;
  order: number;
  isCompleted: boolean;
}

export interface AssignedStaff {
  staffId: string;
  staffName: string;
  startTime?: string;
  endTime?: string;
  role?: string;
}

export interface JobAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface CustomField {
  id: string;
  key: string;
  value: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'select';
  options?: string[]; // For select type
}

export interface JobAttachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: string;
}

export interface JobType {
  id: string;
  name: string;
  description?: string;
  defaultDuration?: number; // in minutes
  defaultFields: CustomField[];
  isActive: boolean;
}

const JOBS_STORAGE_KEY = 'ejp_jobs';
const JOB_STATUSES_STORAGE_KEY = 'ejp_job_statuses';
const JOB_TYPES_STORAGE_KEY = 'ejp_job_types';

// Default job statuses (Kanban pipeline)
const DEFAULT_JOB_STATUSES: JobStatus[] = [
  { id: '1', name: 'Backlog', color: '#6B7280', order: 1, isCompleted: false },
  { id: '2', name: 'Scheduled', color: '#3B82F6', order: 2, isCompleted: false },
  { id: '3', name: 'In Progress', color: '#F59E0B', order: 3, isCompleted: false },
  { id: '4', name: 'Completed', color: '#10B981', order: 4, isCompleted: true },
  { id: '5', name: 'Cancelled', color: '#EF4444', order: 5, isCompleted: true }
];

// Helper functions
function getJobsFromStorage(): Job[] {
  const stored = localStorage.getItem(JOBS_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveJobsToStorage(jobs: Job[]): void {
  localStorage.setItem(JOBS_STORAGE_KEY, JSON.stringify(jobs));
}

function getJobStatusesFromStorage(): JobStatus[] {
  const stored = localStorage.getItem(JOB_STATUSES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : DEFAULT_JOB_STATUSES;
}

function saveJobStatusesToStorage(statuses: JobStatus[]): void {
  localStorage.setItem(JOB_STATUSES_STORAGE_KEY, JSON.stringify(statuses));
}

function getJobTypesFromStorage(): JobType[] {
  const stored = localStorage.getItem(JOB_TYPES_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
}

function saveJobTypesToStorage(types: JobType[]): void {
  localStorage.setItem(JOB_TYPES_STORAGE_KEY, JSON.stringify(types));
}

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Job CRUD operations
export async function getJobs(): Promise<Job[]> {
  // TODO: API Integration - GET /api/jobs
  // Expected response: { success: boolean, data: Job[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getJobsFromStorage());
    }, 100);
  });
}

export async function getJobById(id: string): Promise<Job | null> {
  // TODO: API Integration - GET /api/jobs/{id}
  // Expected response: { success: boolean, data: Job }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const job = jobs.find(j => j.id === id) || null;
      resolve(job);
    }, 100);
  });
}

export async function getJobsByCustomer(customerId: string): Promise<Job[]> {
  // TODO: API Integration - GET /api/jobs?customerId={customerId}
  // Expected response: { success: boolean, data: Job[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const customerJobs = jobs.filter(j => j.customerId === customerId);
      resolve(customerJobs);
    }, 100);
  });
}

export async function createJob(jobData: Omit<Job, 'id' | 'createdAt' | 'updatedAt'>): Promise<Job> {
  // TODO: API Integration - POST /api/jobs
  // Expected request: { job: JobData }
  // Expected response: { success: boolean, data: Job }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const newJob: Job = {
        ...jobData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      jobs.push(newJob);
      saveJobsToStorage(jobs);
      resolve(newJob);
    }, 100);
  });
}

export async function updateJob(id: string, updates: Partial<Job>): Promise<Job> {
  // TODO: API Integration - PUT /api/jobs/{id}
  // Expected request: { job: Partial<Job> }
  // Expected response: { success: boolean, data: Job }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const index = jobs.findIndex(j => j.id === id);
      
      if (index === -1) {
        reject(new Error('Job not found'));
        return;
      }
      
      jobs[index] = {
        ...jobs[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      saveJobsToStorage(jobs);
      resolve(jobs[index]);
    }, 100);
  });
}

export async function deleteJob(id: string): Promise<void> {
  // TODO: API Integration - DELETE /api/jobs/{id}
  // Expected response: { success: boolean }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const index = jobs.findIndex(j => j.id === id);
      
      if (index === -1) {
        reject(new Error('Job not found'));
        return;
      }
      
      jobs.splice(index, 1);
      saveJobsToStorage(jobs);
      resolve();
    }, 100);
  });
}

// Job Status management
export async function getJobStatuses(): Promise<JobStatus[]> {
  // TODO: API Integration - GET /api/job-statuses
  // Expected response: { success: boolean, data: JobStatus[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getJobStatusesFromStorage());
    }, 100);
  });
}

export async function updateJobStatus(id: string, statusId: string): Promise<Job> {
  // TODO: API Integration - PUT /api/jobs/{id}/status
  // Expected request: { statusId: string }
  // Expected response: { success: boolean, data: Job }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      const statuses = getJobStatusesFromStorage();
      const jobIndex = jobs.findIndex(j => j.id === id);
      const status = statuses.find(s => s.id === statusId);
      
      if (jobIndex === -1) {
        reject(new Error('Job not found'));
        return;
      }
      
      if (!status) {
        reject(new Error('Status not found'));
        return;
      }
      
      jobs[jobIndex].status = status;
      jobs[jobIndex].updatedAt = new Date().toISOString();
      
      if (status.isCompleted && !jobs[jobIndex].completedAt) {
        jobs[jobIndex].completedAt = new Date().toISOString();
      } else if (!status.isCompleted) {
        jobs[jobIndex].completedAt = undefined;
      }
      
      saveJobsToStorage(jobs);
      resolve(jobs[jobIndex]);
    }, 100);
  });
}

export async function createJobStatus(statusData: Omit<JobStatus, 'id'>): Promise<JobStatus> {
  // TODO: API Integration - POST /api/job-statuses
  // Expected request: { status: JobStatusData }
  // Expected response: { success: boolean, data: JobStatus }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const statuses = getJobStatusesFromStorage();
      const newStatus: JobStatus = {
        ...statusData,
        id: generateId()
      };
      
      statuses.push(newStatus);
      statuses.sort((a, b) => a.order - b.order);
      saveJobStatusesToStorage(statuses);
      resolve(newStatus);
    }, 100);
  });
}

export async function updateJobStatusOrder(statusUpdates: { id: string; order: number }[]): Promise<JobStatus[]> {
  // TODO: API Integration - PUT /api/job-statuses/reorder
  // Expected request: { statusUpdates: { id: string; order: number }[] }
  // Expected response: { success: boolean, data: JobStatus[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const statuses = getJobStatusesFromStorage();
      
      statusUpdates.forEach(update => {
        const status = statuses.find(s => s.id === update.id);
        if (status) {
          status.order = update.order;
        }
      });
      
      statuses.sort((a, b) => a.order - b.order);
      saveJobStatusesToStorage(statuses);
      resolve(statuses);
    }, 100);
  });
}

// Job Types management
export async function getJobTypes(): Promise<JobType[]> {
  // TODO: API Integration - GET /api/job-types
  // Expected response: { success: boolean, data: JobType[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getJobTypesFromStorage());
    }, 100);
  });
}

export async function createJobType(typeData: Omit<JobType, 'id'>): Promise<JobType> {
  // TODO: API Integration - POST /api/job-types
  // Expected request: { jobType: JobTypeData }
  // Expected response: { success: boolean, data: JobType }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const types = getJobTypesFromStorage();
      const newType: JobType = {
        ...typeData,
        id: generateId()
      };
      
      types.push(newType);
      saveJobTypesToStorage(types);
      resolve(newType);
    }, 100);
  });
}

export async function updateJobType(id: string, updates: Partial<JobType>): Promise<JobType> {
  // TODO: API Integration - PUT /api/job-types/{id}
  // Expected request: { jobType: Partial<JobType> }
  // Expected response: { success: boolean, data: JobType }
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const types = getJobTypesFromStorage();
      const index = types.findIndex(t => t.id === id);
      
      if (index === -1) {
        reject(new Error('Job type not found'));
        return;
      }
      
      types[index] = { ...types[index], ...updates };
      saveJobTypesToStorage(types);
      resolve(types[index]);
    }, 100);
  });
}

// Get uninvoiced jobs for a customer
export async function getUninvoicedJobs(customerId: string): Promise<Job[]> {
  // TODO: API Integration - GET /api/jobs/uninvoiced?customerId={customerId}
  // Expected response: { success: boolean, data: Job[] }
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const jobs = getJobsFromStorage();
      // For now, assume jobs without completedAt are uninvoiced
      // In real implementation, this would check against invoice line items
      const uninvoicedJobs = jobs.filter(j => 
        j.customerId === customerId && 
        j.status.isCompleted && 
        j.completedAt
        // && !j.invoiced // This would be a real field in the API
      );
      resolve(uninvoicedJobs);
    }, 100);
  });
} 