import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// Define user type
export interface User {
  email: string;
  name?: string;
}

// Initialize the auth store with data from localStorage if available
const storedUser = browser && localStorage.getItem('user');
const initialUser = storedUser ? JSON.parse(storedUser) : null;

// Create the auth store
export const user = writable<User | null>(initialUser);

// Subscribe to changes and update localStorage
if (browser) {
  user.subscribe((value) => {
    if (value) {
      localStorage.setItem('user', JSON.stringify(value));
    } else {
      localStorage.removeItem('user');
    }
  });
}

// Authentication functions
export function login(email: string, password: string): boolean {
  // In a real app, you would validate credentials against a backend
  // For this example, we'll accept any email with a password length > 5
  if (email && password.length > 5) {
    user.set({ email });
    return true;
  }
  return false;
}

export function logout() {
  user.set(null);
}

export function isLoggedIn(): boolean {
  let loggedIn = false;
  user.subscribe(value => {
    loggedIn = !!value;
  })();
  return loggedIn;
}
