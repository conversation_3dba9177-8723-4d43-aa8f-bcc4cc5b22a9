<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import {
    getInvoiceTemplates,
    createInvoiceTemplate,
    updateInvoiceTemplate,
    deleteInvoiceTemplate,
    type InvoiceTemplate,
    type TemplateSection
  } from '$lib/api/invoices';

  const dispatch = createEventDispatcher();

  // Props
  export let isLoading = false;

  // State
  let templates: InvoiceTemplate[] = [];
  let isEditing = false;
  let selectedTemplate: InvoiceTemplate | null = null;
  let showPreview = true;
  let isSaving = false;
  let logoFileInput: HTMLInputElement;

  // Template form data
  let templateForm: {
    name: string;
    isDefault: boolean;
    logoUrl: string;
    colorScheme: {
      primary: string;
      secondary: string;
      accent: string;
      background?: string;
      text?: string;
    };
    sections: TemplateSection[];
  } = {
    name: '',
    isDefault: false,
    logoUrl: '',
    colorScheme: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#10b981',
      background: '#ffffff',
      text: '#000000'
    },
    sections: [
      {
        id: 'header',
        type: 'header',
        content: 'INVOICE',
        order: 1,
        isVisible: true
      },
      {
        id: 'terms',
        type: 'terms',
        content: 'Payment due within 30 days. Please make payment via bank transfer.',
        order: 2,
        isVisible: true
      },
      {
        id: 'footer',
        type: 'footer',
        content: 'Thank you for your business!',
        order: 3,
        isVisible: true
      }
    ]
  };

  // Load templates on component mount
  async function loadTemplates() {
    try {
      templates = await getInvoiceTemplates();
    } catch (error) {
      console.error('Error loading templates:', error);
      addToast({ message: 'Failed to load templates', type: 'error' });
    }
  }

  // Initialize templates when component mounts
  loadTemplates();

  // Template designer functions
  function startNewTemplate() {
    selectedTemplate = null;
    isEditing = true;
    resetForm();
  }

  function editTemplate(template: InvoiceTemplate) {
    selectedTemplate = template;
    isEditing = true;
    populateForm(template);
  }

  function resetForm() {
    templateForm = {
      name: '',
      isDefault: false,
      logoUrl: '',
      colorScheme: {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#10b981',
        background: '#ffffff',
        text: '#000000'
      },
      sections: [
        {
          id: 'header',
          type: 'header' as const,
          content: 'INVOICE',
          order: 1,
          isVisible: true
        },
        {
          id: 'terms',
          type: 'terms' as const,
          content: 'Payment due within 30 days. Please make payment via bank transfer.',
          order: 2,
          isVisible: true
        },
        {
          id: 'footer',
          type: 'footer' as const,
          content: 'Thank you for your business!',
          order: 3,
          isVisible: true
        }
      ]
    };
  }

  function populateForm(template: InvoiceTemplate) {
    templateForm = {
      name: template.name,
      isDefault: template.isDefault,
      logoUrl: template.logoUrl || '',
      colorScheme: template.colorScheme || {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#10b981'
      },
      sections: template.sections || []
    };
  }

  function handleLogoUpload(event: Event) {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];

    if (file) {
      // In a real app, you would upload to a server
      // For now, we'll create a data URL
      const reader = new FileReader();
      reader.onload = (e) => {
        templateForm.logoUrl = e.target?.result as string;
      };
      reader.readAsDataURL(file);

      addToast({ message: 'Logo uploaded successfully', type: 'success' });
    }
  }

  function addTextSection() {
    const newSection = {
      id: `section_${Date.now()}`,
      type: 'custom' as const,
      content: 'Enter your text here...',
      order: templateForm.sections.length + 1,
      isVisible: true
    };

    templateForm.sections = [...templateForm.sections, newSection];
  }

  function removeTextSection(index: number) {
    templateForm.sections = templateForm.sections.filter((_, i) => i !== index);
  }

  async function saveTemplate() {
    if (!templateForm.name.trim()) {
      addToast({ message: 'Template name is required', type: 'error' });
      return;
    }

    isSaving = true;
    try {
      const templateData = {
        name: templateForm.name,
        isDefault: templateForm.isDefault,
        logoUrl: templateForm.logoUrl,
        colorScheme: templateForm.colorScheme,
        sections: templateForm.sections
      };

      if (selectedTemplate) {
        await updateInvoiceTemplate(selectedTemplate.id, templateData);
        addToast({ message: 'Template updated successfully', type: 'success' });
      } else {
        await createInvoiceTemplate(templateData);
        addToast({ message: 'Template created successfully', type: 'success' });
      }

      await loadTemplates();
      cancelEdit();
      dispatch('templateSaved');
    } catch (error) {
      console.error('Error saving template:', error);
      addToast({ message: 'Failed to save template', type: 'error' });
    } finally {
      isSaving = false;
    }
  }

  function cancelEdit() {
    isEditing = false;
    selectedTemplate = null;
    showPreview = false;
  }

  function togglePreview() {
    showPreview = !showPreview;
  }

  async function deleteTemplate(template: InvoiceTemplate) {
    if (!confirm(`Are you sure you want to delete "${template.name}"?`)) {
      return;
    }

    try {
      await deleteInvoiceTemplate(template.id);
      addToast({ message: 'Template deleted successfully', type: 'success' });
      await loadTemplates();
      dispatch('templateDeleted');
    } catch (error) {
      console.error('Error deleting template:', error);
      addToast({ message: 'Failed to delete template', type: 'error' });
    }
  }
</script>

<!-- Template Designer Content -->
{#if isLoading}
  <div class="loading-container">
    <LoadingSpinner />
    <p>Loading templates...</p>
  </div>
{:else}
  <div class="designer-container">
    {#if !isEditing}
      <!-- Template List View -->
      <div class="templates-section">
        <div class="section-header">
          <h2>Invoice Templates</h2>
          <Button variant="primary" on:click={startNewTemplate}>
            Create New Template
          </Button>
        </div>

        {#if templates.length === 0}
          <div class="empty-state">
            <p>No templates found. Create your first invoice template to get started.</p>
            <Button variant="primary" on:click={startNewTemplate}>
              Create Template
            </Button>
          </div>
        {:else}
          <div class="templates-grid">
            {#each templates as template}
              <div class="template-card">
                <div class="template-preview">
                  {#if template.logoUrl}
                    <img src={template.logoUrl} alt="Logo" class="template-logo" />
                  {/if}
                  <div class="template-header" style="color: {template.colorScheme?.primary || '#2563eb'}">
                    INVOICE
                  </div>
                  <div class="template-content">
                    <div class="sample-line"></div>
                    <div class="sample-line short"></div>
                    <div class="sample-line"></div>
                  </div>
                </div>

                <div class="template-info">
                  <h3>{template.name}</h3>
                  {#if template.isDefault}
                    <span class="default-badge">Default</span>
                  {/if}
                </div>

                <div class="template-actions">
                  <Button variant="secondary" size="small" on:click={() => editTemplate(template)}>
                    Edit
                  </Button>
                  <Button variant="tertiary" size="small" on:click={() => deleteTemplate(template)}>
                    Delete
                  </Button>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    {:else}
      <!-- Template Editor View -->
      <div class="editor-container">
        <div class="editor-header">
          <h2>{selectedTemplate ? 'Edit Template' : 'Create New Template'}</h2>
          <div class="editor-actions">
            <Button variant="secondary" on:click={togglePreview}>
              {showPreview ? 'Hide Preview' : 'Show Preview'}
            </Button>
            <Button variant="tertiary" on:click={cancelEdit}>Cancel</Button>
            <Button variant="primary" on:click={saveTemplate} disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save Template'}
            </Button>
          </div>
        </div>

        <div class="editor-content" class:with-preview={showPreview}>
          <!-- Editor Panel -->
          <div class="editor-panel">
            <!-- Basic Settings -->
            <div class="editor-section">
              <h3>Basic Settings</h3>

              <div class="form-group">
                <label for="templateName">Template Name</label>
                <input
                  type="text"
                  id="templateName"
                  bind:value={templateForm.name}
                  placeholder="Enter template name"
                />
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input
                    type="checkbox"
                    bind:checked={templateForm.isDefault}
                  />
                  Set as default template
                </label>
              </div>
            </div>

            <!-- Logo Upload -->
            <div class="editor-section">
              <h3>Logo</h3>

              <div class="logo-upload">
                {#if templateForm.logoUrl}
                  <div class="logo-preview">
                    <img src={templateForm.logoUrl} alt="Logo preview" />
                    <Button variant="tertiary" size="small" on:click={() => templateForm.logoUrl = ''}>
                      Remove
                    </Button>
                  </div>
                {:else}
                  <div class="logo-placeholder">
                    <p>No logo uploaded</p>
                  </div>
                {/if}

                <input
                  type="file"
                  accept="image/*"
                  bind:this={logoFileInput}
                  on:change={handleLogoUpload}
                  style="display: none;"
                />

                <Button variant="secondary" on:click={() => logoFileInput.click()}>
                  Upload Logo
                </Button>
              </div>
            </div>

            <!-- Color Scheme -->
            <div class="editor-section">
              <h3>Color Scheme</h3>

              <div class="color-grid">
                <div class="color-input">
                  <label for="primaryColor">Primary Color</label>
                  <input
                    type="color"
                    id="primaryColor"
                    bind:value={templateForm.colorScheme.primary}
                  />
                </div>

                <div class="color-input">
                  <label for="secondaryColor">Secondary Color</label>
                  <input
                    type="color"
                    id="secondaryColor"
                    bind:value={templateForm.colorScheme.secondary}
                  />
                </div>

                <div class="color-input">
                  <label for="accentColor">Accent Color</label>
                  <input
                    type="color"
                    id="accentColor"
                    bind:value={templateForm.colorScheme.accent}
                  />
                </div>
              </div>
            </div>

            <!-- Text Sections -->
            <div class="editor-section">
              <div class="section-header">
                <h3>Text Sections</h3>
                <Button variant="secondary" size="small" on:click={addTextSection}>
                  Add Section
                </Button>
              </div>

              {#each templateForm.sections as section, index}
                <div class="text-section">
                  <div class="section-controls">
                    <input
                      type="text"
                      bind:value={section.content}
                      placeholder="Section content"
                      class="section-name"
                    />

                    <label class="checkbox-label">
                      <input
                        type="checkbox"
                        bind:checked={section.isVisible}
                      />
                      Visible
                    </label>

                    <Button variant="tertiary" size="small" on:click={() => removeTextSection(index)}>
                      Remove
                    </Button>
                  </div>
                </div>
              {/each}
            </div>
          </div>

          <!-- Preview Panel -->
          {#if showPreview}
            <div class="preview-panel">
              <h3>Template Preview</h3>
              <div class="preview-content" style="background: {templateForm.colorScheme.background}; color: {templateForm.colorScheme.text}">
                {#if templateForm.logoUrl}
                  <img src={templateForm.logoUrl} alt="Logo" class="preview-logo" />
                {/if}

                <div class="preview-header" style="color: {templateForm.colorScheme.primary}">
                  INVOICE
                </div>

                {#each templateForm.sections.filter(s => s.isVisible) as section}
                  <div class="preview-section">
                    {section.content}
                  </div>
                {/each}
              </div>
            </div>
          {/if}
        </div>
      </div>
    {/if}
  </div>
{/if}

<style lang="less">
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .designer-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    h2 {
      margin: 0;
      color: var(--black);
    }
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    p {
      margin: 0 0 2rem 0;
      color: var(--grey);
    }
  }

  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .template-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;
    transition: box-shadow 0.2s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .template-preview {
      padding: 2rem;
      background: #f8f9fa;
      border-bottom: 1px solid var(--border);
      min-height: 200px;
      position: relative;

      .template-logo {
        max-width: 60px;
        max-height: 40px;
        margin-bottom: 1rem;
      }

      .template-header {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
      }

      .template-content {
        .sample-line {
          height: 8px;
          background: #e5e7eb;
          margin-bottom: 0.5rem;
          border-radius: 4px;

          &.short {
            width: 60%;
          }
        }
      }
    }

    .template-info {
      padding: 1rem;

      h3 {
        margin: 0 0 0.5rem 0;
        color: var(--black);
      }

      .default-badge {
        background: var(--primary);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: var(--br);
        font-size: 0.75rem;
        font-weight: 500;
      }
    }

    .template-actions {
      padding: 1rem;
      border-top: 1px solid var(--border);
      display: flex;
      gap: 0.5rem;
    }
  }

  .editor-container {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;
  }

  .editor-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      color: var(--black);
    }

    .editor-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .editor-content {
    display: grid;
    grid-template-columns: 1fr;
    min-height: 600px;

    &.with-preview {
      grid-template-columns: 1fr 1fr;
    }
  }

  .editor-panel {
    padding: 2rem;
    border-right: 1px solid var(--border);
    overflow-y: auto;
    max-height: 80vh;
  }

  .editor-section {
    margin-bottom: 2rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
      font-size: 1.1rem;
    }

    .form-group {
      margin-bottom: 1rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--black);

        &.checkbox-label {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          cursor: pointer;

          input[type="checkbox"] {
            margin: 0;
          }
        }
      }

      input[type="text"],
      input[type="number"],
      select,
      textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }
  }

  .logo-upload {
    .logo-preview {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;

      img {
        max-width: 100px;
        max-height: 60px;
        border: 1px solid var(--border);
        border-radius: var(--br);
      }
    }

    .logo-placeholder {
      padding: 2rem;
      border: 2px dashed var(--border);
      border-radius: var(--br);
      text-align: center;
      margin-bottom: 1rem;

      p {
        margin: 0;
        color: var(--grey);
      }
    }
  }

  .color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;

    .color-input {
      label {
        display: block;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
      }

      input[type="color"] {
        width: 100%;
        height: 40px;
        border: 1px solid var(--border);
        border-radius: var(--br);
        cursor: pointer;
      }
    }
  }

  .text-section {
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1rem;
    margin-bottom: 1rem;

    .section-controls {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;

      .section-name {
        flex: 1;
      }

      .checkbox-label {
        white-space: nowrap;
      }
    }
  }

  .preview-panel {
    padding: 2rem;
    background: #f8f9fa;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    .preview-content {
      background: white;
      padding: 2rem;
      border-radius: var(--br);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .preview-logo {
        max-width: 120px;
        max-height: 80px;
        margin-bottom: 1rem;
      }

      .preview-header {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 2rem;
        text-align: center;
      }

      .preview-section {
        margin-bottom: 1rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e5e7eb;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
</style>
