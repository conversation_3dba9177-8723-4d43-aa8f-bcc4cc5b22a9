<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import Tabs from '$lib/components/Tabs.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import {
    getInvoiceTemplates,
    createInvoiceTemplate,
    updateInvoiceTemplate,
    deleteInvoiceTemplate,
    type InvoiceTemplate,
    type TemplateSection,
    type TemplateComponent,
    type TemplateComponents
  } from '$lib/api/invoices';

  const dispatch = createEventDispatcher();

  // Props
  export let isLoading = false;

  // State
  let templates: InvoiceTemplate[] = [];
  let isEditing = false;
  let selectedTemplate: InvoiceTemplate | null = null;
  let isSaving = false;
  let logoFileInput: HTMLInputElement;
  let draggedComponent: string | null = null;
  let selectedComponent: string | null = null;
  let dragPlaceholder: { rowIndex: number; columnIndex: number } | null = null;
  let draggedRow: number | null = null;
  let rowDropPlaceholder: number | null = null;
  let activeTab = 'properties';

  // Tab configuration
  const propertiesTabs = [
    { id: 'properties', label: 'Properties' },
    { id: 'layout', label: 'Layout' },
    { id: 'add', label: 'Add' }
  ];

  // Row-based layout system
  interface LayoutRow {
    id: string;
    columns: number; // 1, 2, or 3
    components: string[][]; // arrays of component IDs for each column
  }

  let layoutRows: LayoutRow[] = [
    {
      id: 'row1',
      columns: 2,
      components: [['title'], ['buyerLogo']]
    },
    {
      id: 'row2',
      columns: 2,
      components: [['sellerDetails'], ['headerFields']]
    },
    {
      id: 'row3',
      columns: 1,
      components: [['buyerDetails']]
    },
    {
      id: 'row4',
      columns: 1,
      components: [['lineItems']]
    },
    {
      id: 'row5',
      columns: 2,
      components: [['terms'], ['notes']]
    }
  ];

  // Available components that can be dragged into slots
  let availableComponents: TemplateComponents = {
    title: {
      id: 'title',
      type: 'title' as const,
      content: 'INVOICE',
      x: 0, y: 0, width: 0, height: 0, // These will be calculated based on row/column
      fontSize: 32,
      fontWeight: 'bold',
      color: '#2563eb',
      fontFamily: 'Arial, sans-serif'
    },
    buyerLogo: {
      id: 'buyerLogo',
      type: 'logo' as const,
      content: '',
      x: 0, y: 0, width: 0, height: 0,
      logoUrl: ''
    },
    sellerDetails: {
      id: 'sellerDetails',
      type: 'text' as const,
      content: 'Your Company Name\n123 Business Street\nCity, State 12345\nPhone: (*************\nEmail: <EMAIL>',
      x: 0, y: 0, width: 0, height: 0,
      fontSize: 12,
      fontWeight: 'normal',
      color: '#000000',
      fontFamily: 'Arial, sans-serif'
    },
    buyerDetails: {
      id: 'buyerDetails',
      type: 'text' as const,
      content: 'Bill To:\nClient Company Name\n456 Client Street\nClient City, State 67890',
      x: 0, y: 0, width: 0, height: 0,
      fontSize: 12,
      fontWeight: 'normal',
      color: '#000000',
      fontFamily: 'Arial, sans-serif'
    },
    headerFields: {
      id: 'headerFields',
      type: 'fields' as const,
      content: '',
      x: 0, y: 0, width: 0, height: 0,
      fontSize: 12,
      fontWeight: 'normal',
      color: '#000000',
      fontFamily: 'Arial, sans-serif',
      fields: [
        { label: 'Invoice #:', value: 'INV-001' },
        { label: 'Date:', value: '2024-01-15' },
        { label: 'Due Date:', value: '2024-02-15' },
        { label: 'Terms:', value: 'Net 30' }
      ]
    },
    lineItems: {
      id: 'lineItems',
      type: 'table' as const,
      content: '',
      x: 0, y: 0, width: 0, height: 0,
      fontSize: 12,
      fontWeight: 'normal',
      color: '#000000',
      fontFamily: 'Arial, sans-serif',
      headerColor: '#f3f4f6',
      borderColor: '#e5e7eb'
    },
    terms: {
      id: 'terms',
      type: 'text' as const,
      content: 'Terms & Conditions:\nPayment is due within 30 days of invoice date. Late payments may incur additional charges.',
      x: 0, y: 0, width: 0, height: 0,
      fontSize: 10,
      fontWeight: 'normal',
      color: '#666666',
      fontFamily: 'Arial, sans-serif'
    },
    notes: {
      id: 'notes',
      type: 'text' as const,
      content: 'Notes:\nThank you for your business!',
      x: 0, y: 0, width: 0, height: 0,
      fontSize: 10,
      fontWeight: 'normal',
      color: '#666666',
      fontFamily: 'Arial, sans-serif'
    }
  };

  // Template form data
  let templateForm = {
    name: '',
    isDefault: false,
    components: { ...availableComponents },
    layout: [...layoutRows]
  };

  // Load templates on component mount
  async function loadTemplates() {
    try {
      templates = await getInvoiceTemplates();
    } catch (error) {
      console.error('Error loading templates:', error);
      addToast({ message: 'Failed to load templates', type: 'error' });
    }
  }

  // Initialize templates when component mounts
  loadTemplates();

  // Template designer functions
  function startNewTemplate() {
    selectedTemplate = null;
    isEditing = true;
    resetForm();
  }

  function editTemplate(template: InvoiceTemplate) {
    selectedTemplate = template;
    isEditing = true;
    populateForm(template);
  }

  function resetForm() {
    templateForm = {
      name: '',
      isDefault: false,
      components: { ...availableComponents },
      layout: [...layoutRows]
    };
    selectedComponent = null;
  }

  function populateForm(template: InvoiceTemplate) {
    templateForm = {
      name: template.name,
      isDefault: template.isDefault,
      components: template.components || { ...availableComponents },
      layout: (template as any).layout || [...layoutRows]
    };

    // Clean up any invalid component references
    cleanupLayout();
  }

  // Row management functions
  function addRow() {
    const newRow: LayoutRow = {
      id: `row_${Date.now()}`,
      columns: 1,
      components: [[]]
    };
    templateForm.layout = [...templateForm.layout, newRow];
  }

  function removeRow(rowIndex: number) {
    templateForm.layout = templateForm.layout.filter((_, i) => i !== rowIndex);
  }

  // Row drag and drop functions
  function handleRowDragStart(event: DragEvent, rowIndex: number) {
    if (!event.dataTransfer) return;

    draggedRow = rowIndex;
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', rowIndex.toString());

    const dragHandle = event.target as HTMLElement;
    dragHandle.style.opacity = '0.5';
  }

  function handleRowDragOver(event: DragEvent, targetIndex: number) {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }

    rowDropPlaceholder = targetIndex;
  }

  function handleRowDragLeave(event: DragEvent) {
    const relatedTarget = event.relatedTarget as HTMLElement;
    const currentTarget = event.currentTarget as HTMLElement;

    if (!currentTarget.contains(relatedTarget)) {
      rowDropPlaceholder = null;
    }
  }

  function handleRowDrop(event: DragEvent, targetIndex: number) {
    event.preventDefault();
    event.stopPropagation();

    if (draggedRow === null) return;

    // Reorder the rows
    const newLayout = [...templateForm.layout];
    const draggedRowData = newLayout[draggedRow];

    // Remove from old position
    newLayout.splice(draggedRow, 1);

    // Insert at new position (adjust index if dragging down)
    const insertIndex = draggedRow < targetIndex ? targetIndex - 1 : targetIndex;
    newLayout.splice(insertIndex, 0, draggedRowData);

    templateForm.layout = newLayout;

    // Clear drag state
    draggedRow = null;
    rowDropPlaceholder = null;
  }

  function handleRowDragEnd(event: DragEvent) {
    const target = event.target as HTMLElement;
    target.style.opacity = '1';
    draggedRow = null;
    rowDropPlaceholder = null;
  }

  function handleTabChange(event: CustomEvent<{ tabId: string }>) {
    activeTab = event.detail.tabId;
  }

  function updateRowColumns(rowIndex: number, columns: number) {
    const row = templateForm.layout[rowIndex];
    const currentComponents = row.components.slice(0, columns);

    // Pad with empty arrays if we're increasing columns
    while (currentComponents.length < columns) {
      currentComponents.push([]);
    }

    templateForm.layout[rowIndex] = {
      ...row,
      columns,
      components: currentComponents
    };
  }



  // Drag and drop functions for components
  function handleDragStart(event: DragEvent, componentId: string) {
    if (!event.dataTransfer) return;

    draggedComponent = componentId;
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', componentId);

    // Add visual feedback to the drag handle
    const dragHandle = event.target as HTMLElement;
    dragHandle.style.opacity = '0.5';
  }

  function handleSlotDragOver(event: DragEvent, rowIndex: number, columnIndex: number) {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }

    // Show placeholder
    dragPlaceholder = { rowIndex, columnIndex };
  }

  function handleSlotDragLeave(event: DragEvent) {
    // Only clear placeholder if we're actually leaving the drop zone area
    const relatedTarget = event.relatedTarget as HTMLElement;
    const currentTarget = event.currentTarget as HTMLElement;

    if (!currentTarget.contains(relatedTarget)) {
      dragPlaceholder = null;
    }
  }

  function handleSlotDrop(event: DragEvent, rowIndex: number, columnIndex: number) {
    event.preventDefault();
    event.stopPropagation();

    const componentId = event.dataTransfer?.getData('text/plain') || draggedComponent;
    if (!componentId) return;

    // Remove component from its current position
    templateForm.layout.forEach(row => {
      row.components.forEach(column => {
        const index = column.indexOf(componentId);
        if (index > -1) {
          column.splice(index, 1);
        }
      });
    });

    // Add component to new position
    templateForm.layout[rowIndex].components[columnIndex].push(componentId);

    // Clear drag state
    draggedComponent = null;
    dragPlaceholder = null;

    // Force reactivity update
    templateForm.layout = [...templateForm.layout];
  }

  function handleDragEnd(event: DragEvent) {
    const target = event.target as HTMLElement;
    target.style.opacity = '1';
    draggedComponent = null;
    dragPlaceholder = null;
  }

  // Move component between slots
  function moveComponent(componentId: string, toRowIndex: number, toColumnIndex: number) {
    // Remove component from its current position
    templateForm.layout.forEach(row => {
      row.components.forEach(column => {
        const index = column.indexOf(componentId);
        if (index > -1) {
          column.splice(index, 1);
        }
      });
    });

    // Add component to new position
    templateForm.layout[toRowIndex].components[toColumnIndex].push(componentId);

    // Force reactivity update
    templateForm.layout = [...templateForm.layout];
  }

  function removeComponentFromSlot(rowIndex: number, columnIndex: number, componentIndex: number) {
    templateForm.layout[rowIndex].components[columnIndex].splice(componentIndex, 1);

    // Force reactivity update
    templateForm.layout = [...templateForm.layout];
  }

  function selectComponent(componentId: string | null) {
    selectedComponent = componentId;

    // Automatically switch to properties tab when a component is selected
    if (componentId) {
      activeTab = 'properties';
    }
  }

  // Clean up invalid component references from layout
  function cleanupLayout() {
    templateForm.layout.forEach(row => {
      row.components.forEach(column => {
        // Remove component IDs that don't exist in templateForm.components
        for (let i = column.length - 1; i >= 0; i--) {
          if (!templateForm.components[column[i]]) {
            column.splice(i, 1);
          }
        }
      });
    });

    // Force reactivity update
    templateForm.layout = [...templateForm.layout];
  }

  function handleLogoUpload(event: Event) {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];

    if (file && selectedComponent === 'buyerLogo') {
      const reader = new FileReader();
      reader.onload = (e) => {
        templateForm.components.buyerLogo.logoUrl = e.target?.result as string;
      };
      reader.readAsDataURL(file);
      addToast({ message: 'Logo uploaded successfully', type: 'success' });
    }
  }

  function updateComponentProperty(componentId: string, property: string, value: any) {
    if (templateForm.components[componentId]) {
      (templateForm.components[componentId] as any)[property] = value;
    }
  }

  function addHeaderField() {
    if (templateForm.components.headerFields.fields) {
      templateForm.components.headerFields.fields = [
        ...templateForm.components.headerFields.fields,
        { label: 'New Field:', value: 'Value' }
      ];
    }
  }

  function removeHeaderField(index: number) {
    if (templateForm.components.headerFields.fields) {
      templateForm.components.headerFields.fields = templateForm.components.headerFields.fields.filter((_, i) => i !== index);
    }
  }

  // Get unused components for the component library
  function getUnusedComponents() {
    const usedComponents = new Set();
    templateForm.layout.forEach(row => {
      row.components.forEach(column => {
        column.forEach(componentId => {
          usedComponents.add(componentId);
        });
      });
    });

    return Object.keys(templateForm.components).filter(id => !usedComponents.has(id));
  }

  // Get component description for display
  function getComponentDescription(componentIds: string[]): string {
    if (!componentIds || componentIds.length === 0) return 'Empty';

    if (componentIds.length === 1) {
      const componentId = componentIds[0];
      const component = templateForm.components[componentId];
      if (!component) return 'Unknown Component';

      switch (component.type) {
        case 'title':
          return `Title: "${component.content || 'Untitled'}"`;
        case 'logo':
          return component.logoUrl ? 'Logo (uploaded)' : 'Logo (placeholder)';
        case 'text':
          const preview = (component.content || '').split('\n')[0];
          return `Text: "${preview.length > 30 ? preview.substring(0, 30) + '...' : preview || 'Empty'}"`;
        case 'fields':
          const fieldCount = component.fields?.length || 0;
          return `Fields (${fieldCount} items)`;
        case 'table':
          return 'Line Items Table';
        default:
          return componentId;
      }
    } else {
      // Filter out undefined components and count valid ones
      const validComponents = componentIds.filter(id => templateForm.components[id]);
      return `${validComponents.length} components`;
    }
  }

  async function saveTemplate() {
    if (!templateForm.name.trim()) {
      addToast({ message: 'Template name is required', type: 'error' });
      return;
    }

    isSaving = true;
    try {
      const templateData = {
        name: templateForm.name,
        isDefault: templateForm.isDefault,
        components: templateForm.components,
        layout: templateForm.layout
      };

      if (selectedTemplate) {
        await updateInvoiceTemplate(selectedTemplate.id, templateData as any);
        addToast({ message: 'Template updated successfully', type: 'success' });
      } else {
        await createInvoiceTemplate(templateData as any);
        addToast({ message: 'Template created successfully', type: 'success' });
      }

      await loadTemplates();
      cancelEdit();
      dispatch('templateSaved');
    } catch (error) {
      console.error('Error saving template:', error);
      addToast({ message: 'Failed to save template', type: 'error' });
    } finally {
      isSaving = false;
    }
  }

  function cancelEdit() {
    isEditing = false;
    selectedTemplate = null;
    selectedComponent = null;
  }

  async function deleteTemplate(template: InvoiceTemplate) {
    if (!confirm(`Are you sure you want to delete "${template.name}"?`)) {
      return;
    }

    try {
      await deleteInvoiceTemplate(template.id);
      addToast({ message: 'Template deleted successfully', type: 'success' });
      await loadTemplates();
      dispatch('templateDeleted');
    } catch (error) {
      console.error('Error deleting template:', error);
      addToast({ message: 'Failed to delete template', type: 'error' });
    }
  }
</script>


<!-- Template Designer Content -->
{#if isLoading}
  <div class="loading-container">
    <LoadingSpinner />
    <p>Loading templates...</p>
  </div>
{:else}

    {#if !isEditing}
    <main>
      <!-- Template List View -->
      <div class="templates-section">
        <div class="section-header">
          <h2>Invoice Templates</h2>
          <Button variant="primary" on:click={startNewTemplate}>
            Create New Template
          </Button>
        </div>

        {#if templates.length === 0}
          <div class="empty-state">
            <p>No templates found. Create your first invoice template to get started.</p>
            <Button variant="primary" on:click={startNewTemplate}>
              Create Template
            </Button>
          </div>
        {:else}
          <div class="templates-grid">
            {#each templates as template}
              <div class="template-card">
                <div class="template-preview">
                  <div class="template-header" style="color: {template.components?.title?.color || '#2563eb'}">
                    {template.components?.title?.content || 'INVOICE'}
                  </div>
                  <div class="template-content">
                    <div class="sample-line"></div>
                    <div class="sample-line short"></div>
                    <div class="sample-line"></div>
                  </div>
                </div>

                <div class="template-info">
                  <h3>{template.name}</h3>
                  {#if template.isDefault}
                    <span class="default-badge">Default</span>
                  {/if}
                </div>

                <div class="template-actions">
                  <Button variant="secondary" size="small" on:click={() => editTemplate(template)}>
                    Edit
                  </Button>
                  <Button variant="tertiary" size="small" on:click={() => deleteTemplate(template)}>
                    Delete
                  </Button>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    </main>
    {:else}
      <main class="designer-main">
      <!-- Template Editor View -->
      <div class="editor-container">
        <div class="editor-header">
          <h2>{selectedTemplate ? 'Edit Template' : 'Create New Template'}</h2>
          <div class="form-group">
            <input
              type="text"
              id="templateName"
              bind:value={templateForm.name}
              placeholder="Enter template name"
            />
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" bind:checked={templateForm.isDefault} />
              Set as default template
            </label>
          </div>
          <div class="editor-actions">
            <Button variant="tertiary" on:click={cancelEdit}>Cancel</Button>
            <Button variant="primary" on:click={saveTemplate} disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save Template'}
            </Button>
          </div>
        </div>

        <div class="editor-content">
          <!-- Properties Panel -->
          <div class="properties-panel">
            <Tabs
              tabs={propertiesTabs}
              bind:activeTab={activeTab}
              on:change={handleTabChange}
              navClass="editor-tabs"
            />

            <div class="tab-content">
              {#if activeTab === 'properties'}
                <!-- Component Properties -->
                {#if selectedComponent && templateForm.components[selectedComponent]}
                  {@const component = templateForm.components[selectedComponent]}
                  <div class="property-section">

                    <p class="component-name">{selectedComponent}</p>

                    <!-- Typography -->
                    {#if component.type !== 'logo'}
                      <div class="property-group">
                        <h4>Typography</h4>
                        <div class="input-group">
                          <label>Font Family</label>
                          <select bind:value={component.fontFamily}>
                            <option value="Arial, sans-serif">Arial</option>
                            <option value="Helvetica, sans-serif">Helvetica</option>
                            <option value="Times New Roman, serif">Times New Roman</option>
                            <option value="Georgia, serif">Georgia</option>
                            <option value="Courier New, monospace">Courier New</option>
                          </select>
                        </div>
                        <div class="input-row">
                          <div class="input-group">
                            <label>Font Size</label>
                            <input
                              type="number"
                              bind:value={component.fontSize}
                              min="8"
                              max="72"
                            />
                          </div>
                          <div class="input-group">
                            <label>Font Weight</label>
                            <select bind:value={component.fontWeight}>
                              <option value="normal">Normal</option>
                              <option value="bold">Bold</option>
                              <option value="lighter">Light</option>
                            </select>
                          </div>
                        </div>
                        <div class="input-group">
                          <label>Color</label>
                          <input type="color" bind:value={component.color} />
                        </div>
                      </div>
                    {/if}

                    <!-- Content -->
                    {#if component.type === 'text' || component.type === 'title'}
                      <div class="property-group">
                        <h4>Content</h4>
                        <div class="input-group">
                        <textarea
                          bind:value={component.content}
                          rows="4"
                          placeholder="Enter content..."
                        ></textarea>
                        </div>
                      </div>
                    {/if}

                    <!-- Logo Upload -->
                    {#if component.type === 'logo'}
                      <div class="property-group">
                        <h4>Logo</h4>
                        {#if component.logoUrl}
                          <div class="logo-preview">
                            <img src={component.logoUrl} alt="Logo preview" />
                            <Button variant="tertiary" size="small" on:click={() => component.logoUrl = ''}>
                              Remove
                            </Button>
                          </div>
                        {/if}
                        <input
                          type="file"
                          accept="image/*"
                          bind:this={logoFileInput}
                          on:change={handleLogoUpload}
                          style="display: none;"
                        />
                        <Button variant="secondary" on:click={() => logoFileInput.click()}>
                          Upload Logo
                        </Button>
                      </div>
                    {/if}

                    <!-- Header Fields -->
                    {#if component.type === 'fields' && component.fields}
                      <div class="property-group">
                        <h4>Fields</h4>
                        {#each component.fields as field, index}
                          <div class="field-row">
                            <input
                              type="text"
                              bind:value={field.label}
                              placeholder="Label"
                              class="field-label"
                            />
                            <input
                              type="text"
                              bind:value={field.value}
                              placeholder="Value"
                              class="field-value"
                            />
                            <Button variant="tertiary" size="small" on:click={() => removeHeaderField(index)}>
                              ×
                            </Button>
                          </div>
                        {/each}
                        <Button variant="secondary" size="small" on:click={addHeaderField}>
                          Add Field
                        </Button>
                      </div>
                    {/if}

                    <!-- Table Properties -->
                    {#if component.type === 'table'}
                      <div class="property-group">
                        <h4>Table Style</h4>
                        <div class="input-group">
                          <label for="header-background-color">Header Background</label>
                          <input type="color" id="header-background-color" bind:value={component.headerColor} />
                        </div>
                        <div class="input-group">
                          <label for="border-color">Border Color</label>
                          <input type="color" id="border-color" bind:value={component.borderColor} />
                        </div>
                      </div>
                    {/if}
                  </div>
                {:else}
                  <div class="property-section">
                    <p class="no-selection">Select a component to edit its properties</p>
                  </div>
                {/if}

              {:else if activeTab === 'layout'}
                <!-- Layout Management -->
                <div class="property-section">

                  {#each templateForm.layout as row, rowIndex}
                    {#if rowDropPlaceholder === rowIndex}
                      <div class="row-drop-placeholder">
                        <span>Drop row here</span>
                      </div>
                    {/if}

                    <div
                      class="row-control-item"
                      class:dragging={draggedRow === rowIndex}
                      on:dragover={(e) => handleRowDragOver(e, rowIndex)}
                      on:dragleave={handleRowDragLeave}
                      on:drop={(e) => handleRowDrop(e, rowIndex)}
                      role="button"
                      tabindex="0"
                    >
                      <div class="row-header">
                        <div
                          class="row-drag-handle"
                          draggable="true"
                          on:dragstart={(e) => handleRowDragStart(e, rowIndex)}
                          on:dragend={handleRowDragEnd}
                          title="Drag to reorder row"
                          role="button"
                          tabindex="0"
                        >
                          ⋮⋮
                        </div>
                        <h4>Row {rowIndex + 1}</h4>
                        <Button variant="tertiary" size="small" on:click={() => removeRow(rowIndex)}>
                          ×
                        </Button>
                      </div>

                      <div class="row-settings">
                        <div class="input-group">
                          <label for="columns-select-{rowIndex}">Columns:</label>
                          <select id="columns-select-{rowIndex}" bind:value={row.columns} on:change={() => updateRowColumns(rowIndex, row.columns)}>
                            <option value={1}>1</option>
                            <option value={2}>2</option>
                            <option value={3}>3</option>
                          </select>
                        </div>
                      </div>

                      <div class="row-components">
                        <h5>Components:</h5>
                        <div class="component-list">
                          {#each Array(row.columns) as _, columnIndex}
                            <div class="component-item">
                              <span class="column-label">Col {columnIndex + 1}:</span>
                              <span class="component-desc">
                                {getComponentDescription(row.components[columnIndex])}
                              </span>
                            </div>
                          {/each}
                        </div>
                      </div>
                    </div>
                  {/each}

                  <Button variant="secondary" on:click={addRow}>
                    Add Row
                  </Button>

                  {#if rowDropPlaceholder === templateForm.layout.length}
                    <div class="row-drop-placeholder">
                      <span>Drop row here</span>
                    </div>
                  {/if}
                </div>

              {:else if activeTab === 'add'}
                <!-- Component Library -->
                <div class="property-section">
                  <p class="section-description">Drag components into the layout below</p>

                  <div class="component-library">
                    {#each getUnusedComponents() as componentId}
                      {@const component = templateForm.components[componentId]}
                      <div class="library-component">
                        <div
                          class="drag-handle"
                          draggable="true"
                          on:dragstart={(e) => handleDragStart(e, componentId)}
                          on:dragend={handleDragEnd}
                          title="Drag to place component"
                          role="button"
                          tabindex="0"
                        >
                          ⋮⋮
                        </div>
                        <div class="component-icon">
                          {#if component.type === 'title'}📄
                          {:else if component.type === 'logo'}🖼️
                          {:else if component.type === 'text'}📝
                          {:else if component.type === 'fields'}📋
                          {:else if component.type === 'table'}📊
                          {/if}
                        </div>
                        <span class="component-name">{componentId}</span>
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}
            </div>
          </div>

          <!-- Layout Designer -->
          <div class="layout-designer">

            <div class="a4-canvas">
              {#each templateForm.layout as row, rowIndex}
                <div class="layout-row">
                  <div class="row-columns" style="grid-template-columns: repeat({row.columns}, 1fr);">
                    {#each Array(row.columns) as _, columnIndex}
                      <div
                        class="column-slot"
                        class:occupied={row.components[columnIndex]}
                        on:dragover={(e) => handleSlotDragOver(e, rowIndex, columnIndex)}
                        on:dragleave={handleSlotDragLeave}
                        on:drop={(e) => handleSlotDrop(e, rowIndex, columnIndex)}
                        role="button"
                        tabindex="0"
                      >
                        {#if dragPlaceholder && dragPlaceholder.rowIndex === rowIndex && dragPlaceholder.columnIndex === columnIndex}
                          <div class="drag-placeholder">
                            <span>Drop here</span>
                          </div>
                        {:else if row.components[columnIndex].length > 0}
                          {#each row.components[columnIndex] as componentId, componentIndex}
                            {@const component = templateForm.components[componentId]}
                            {#if component}
                              <div
                                class="placed-component"
                                class:selected={selectedComponent === componentId}
                                on:click={() => selectComponent(componentId)}
                                on:keydown={(e) => { if (e.key === 'Enter' || e.key === ' ') selectComponent(componentId); }}
                                role="button"
                                tabindex="0"
                              >
                              <div class="component-header">
                                <div
                                  class="drag-handle"
                                  draggable="true"
                                  on:dragstart={(e) => handleDragStart(e, componentId)}
                                  on:dragend={handleDragEnd}
                                  title="Drag to move component"
                                  role="button"
                                  tabindex="0"
                                >
                                  ⋮⋮
                                </div>
                                <button
                                  class="remove-component"
                                  on:click|stopPropagation={() => removeComponentFromSlot(rowIndex, columnIndex, componentIndex)}
                                  title="Remove component"
                                >
                                  ×
                                </button>
                              </div>
                              <div class="component-content" style="
                                font-family: {component.fontFamily || 'Arial, sans-serif'};
                                font-size: {component.fontSize || 12}px;
                                font-weight: {component.fontWeight || 'normal'};
                                color: {component.color || '#000000'};
                              ">
                                {#if component.type === 'title'}
                                  <div class="title-component">{component.content}</div>
                                {:else if component.type === 'logo'}
                                  <div class="logo-component">
                                    {#if component.logoUrl}
                                      <img src={component.logoUrl} alt="Logo" />
                                    {:else}
                                      <div class="logo-placeholder">Logo</div>
                                    {/if}
                                  </div>
                                {:else if component.type === 'text'}
                                  <div class="text-component">
                                    {#each component.content.split('\n') as line}
                                      <div>{line}</div>
                                    {/each}
                                  </div>
                                {:else if component.type === 'fields'}
                                  <div class="fields-component">
                                    {#each component.fields || [] as field}
                                      <div class="field-row">
                                        <span class="field-label">{field.label}</span>
                                        <span class="field-value">{field.value}</span>
                                      </div>
                                    {/each}
                                  </div>
                                {:else if component.type === 'table'}
                                  <div class="table-component">
                                    <table style="border-color: {component.borderColor};">
                                      <thead style="background-color: {component.headerColor};">
                                        <tr>
                                          <th>Description</th>
                                          <th>Qty</th>
                                          <th>Rate</th>
                                          <th>Amount</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        <tr>
                                          <td>Sample Item 1</td>
                                          <td>1</td>
                                          <td>$100.00</td>
                                          <td>$100.00</td>
                                        </tr>
                                        <tr>
                                          <td>Sample Item 2</td>
                                          <td>2</td>
                                          <td>$50.00</td>
                                          <td>$100.00</td>
                                        </tr>
                                      </tbody>
                                    </table>
                                    <div class="totals-section">
                                      <div class="total-row">
                                        <span>Subtotal:</span>
                                        <span>$200.00</span>
                                      </div>
                                      <div class="total-row">
                                        <span>Tax (10%):</span>
                                        <span>$20.00</span>
                                      </div>
                                      <div class="total-row total">
                                        <span>Total:</span>
                                        <span>$220.00</span>
                                      </div>
                                    </div>
                                  </div>
                                {/if}
                              </div>
                              </div>
                            {/if}
                          {/each}
                        {:else}
                          <div class="empty-slot">
                            <span>Drop component here</span>
                          </div>
                        {/if}
                      </div>
                    {/each}
                  </div>
                </div>
              {/each}
            </div>
          </div>
        </div>
      </div>
    </main>
    {/if}

{/if}


<style lang="less">
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  main.designer-main {
    padding: 0px;
  }

  .designer-container {
    max-width: 1400px;
    margin: 0 auto;
    height: 100vh;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    h2 {
      margin: 0;
      color: var(--black);
    }
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;

    p {
      margin: 0 0 2rem 0;
      color: var(--grey);
    }
  }

  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .template-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;
    transition: box-shadow 0.2s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .template-preview {
      padding: 2rem;
      background: #f8f9fa;
      border-bottom: 1px solid var(--border);
      min-height: 200px;

      .template-header {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
      }

      .template-content {
        .sample-line {
          height: 8px;
          background: #e5e7eb;
          margin-bottom: 0.5rem;
          border-radius: 4px;

          &.short {
            width: 60%;
          }
        }
      }
    }

    .template-info {
      padding: 1rem;

      h3 {
        margin: 0 0 0.5rem 0;
        color: var(--black);
      }

      .default-badge {
        background: var(--primary);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: var(--br);
        font-size: 0.75rem;
        font-weight: 500;
      }
    }

    .template-actions {
      padding: 1rem;
      border-top: 1px solid var(--border);
      display: flex;
      gap: 0.5rem;
    }
  }

  .editor-container {
    background: white;
    // border: 1px solid var(--border);
    // border-radius: var(--br);
    overflow: hidden;
    height: calc(100vh - 134px);
    display: flex;
    flex-direction: column;
  }

  .editor-header {
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    gap: 20px;

    h2 {
      margin: 0;
      color: var(--black);
    }

    .editor-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .editor-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    flex: 1;
    overflow: hidden;
  }

  .properties-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
    border-right: 1px solid var(--border);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    box-shadow: inset -2px 0 8px rgba(0, 0, 0, 0.05);

    .tab-content {
      flex: 1;
      // padding: 2rem 1.5rem;
      overflow-y: auto;
    }
  }

  .property-section {
    margin-bottom: 2rem;
    // background: white;
    border-radius: 12px;
    padding: 1.5rem;
    // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    // border: 1px solid rgba(0, 0, 0, 0.05);

    h3 {
      margin: 0 0 1.5rem 0;
      color: var(--text);
      font-size: 1.2rem;
      font-weight: 700;
      display: flex;
      align-items: center;
      gap: 10px;

      &::before {
        content: '⚙️';
        font-size: 1rem;
      }
    }

    .section-description {
      color: var(--grey);
      font-size: 0.9rem;
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }

    .component-name {
      background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.1), rgba(var(--primary-rgb), 0.05));
      color: var(--primary);
      // padding: 1rem 1.25rem;
      border-radius: 10px;
      padding-bottom: 1rem;
      // margin-bottom: 1.5rem;
      font-size: 1rem;
      font-weight: 600;
      text-transform: capitalize;
      // border-left: 4px solid var(--primary);
      display: flex;
      align-items: center;
      gap: 10px;

      &::before {
        content: '📝';
        font-size: 0.9rem;
      }
    }

    .no-selection {
      color: var(--grey);
      font-style: italic;
      text-align: center;
      padding: 2rem;
      background: #f8f9fa;
      border-radius: 8px;
      border: 2px dashed #e9ecef;
    }
  }

  .component-library {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .row-control-item {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s;

    &.dragging {
      opacity: 0.5;
      transform: rotate(2deg);
    }

    .row-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;

      h4 {
        margin: 0;
        color: var(--black);
        font-size: 0.9rem;
        flex: 1;
      }
    }

    .row-settings {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      margin-bottom: 1rem;
    }

    .row-components {
      h5 {
        margin: 0 0 0.5rem 0;
        color: var(--black);
        font-size: 0.8rem;
        font-weight: 600;
      }

      .component-list {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
      }

      .component-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0.5rem;
        background: #f8f9fa;
        border-radius: 4px;
        font-size: 0.75rem;

        .column-label {
          font-weight: 500;
          color: var(--grey);
          min-width: 40px;
        }

        .component-desc {
          color: var(--black);
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .row-drag-handle {
    cursor: grab;
    padding: 0.25rem;
    color: var(--grey);
    font-weight: bold;
    user-select: none;
    transition: all 0.2s;
    border-radius: 4px;

    &:hover {
      background: var(--primary-fade);
      color: var(--primary);
    }

    &:active {
      cursor: grabbing;
    }
  }

  .row-drop-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    background: var(--primary-fade);
    border: 2px dashed var(--primary);
    border-radius: var(--br);
    color: var(--primary);
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    animation: pulse 1.5s ease-in-out infinite;
  }

  .library-component {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    transition: all 0.2s;

    &:hover {
      border-color: var(--primary);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .component-icon {
      font-size: 1.2rem;
    }

    .component-name {
      font-size: 0.9rem;
      text-transform: capitalize;
      color: var(--black);
    }
  }

  .drag-handle {
    cursor: grab;
    padding: 0.25rem;
    color: var(--grey);
    font-weight: bold;
    user-select: none;
    transition: all 0.2s;
    border-radius: 4px;
    background: #eee;
    z-index: 100;

    &:hover {
      background: var(--primary-fade);
      color: var(--primary);
    }

    &:active {
      cursor: grabbing;
    }
  }

  .property-group {
    margin-bottom: 2rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;

    h4 {
      margin: 0 0 1rem 0;
      color: var(--text);
      font-size: .8rem;
      font-weight: 500;


      display: flex;
      align-items: center;
      gap: 8px;
      padding-bottom: 0.75rem;
      border-bottom: 2px solid #e9ecef;


    }
  }

  .form-group {
    margin-bottom: 0px;

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: var(--black);
      font-size: 0.9rem;

      &.checkbox-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        margin-bottom: 0;

        input[type="checkbox"] {
          margin: 0;
          width: auto;
        }
      }
    }

    input[type="text"],
    input[type="number"],
    select,
    textarea {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
      font-size: 0.9rem;

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px var(--primary-fade);
      }
    }
  }

  .input-group {
    margin-bottom: 1.25rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: var(--text);
      font-size: 0.8rem;
      font-weight: 500;
      opacity: 0.8;
    }

    input, select, textarea {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      background-color: white;
      font-family: inherit;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
        transform: translateY(-1px);
      }

      &:hover {
        border-color: rgba(var(--primary-rgb), 0.3);
      }
    }

    input[type="color"] {
      height: 48px;
      padding: 6px;
      cursor: pointer;
      border-radius: 8px;

      &::-webkit-color-swatch {
        border-radius: 4px;
        border: none;
      }

      &::-webkit-color-swatch-wrapper {
        border-radius: 4px;
        padding: 0;
      }
    }

    input[type="number"] {
      text-align: center;
      font-weight: 600;
    }

    select {
      cursor: pointer;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
      background-position: right 12px center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      appearance: none;
    }

    textarea {
      resize: vertical;
      min-height: 100px;
      font-family: inherit;
      line-height: 1.5;
    }
  }

  .input-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .field-row {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 12px;
    align-items: end;
    margin-bottom: 12px;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-color: rgba(var(--primary-rgb), 0.2);
    }

    .field-label,
    .field-value {
      flex: 1;
      padding: 0.75rem;
      border: 2px solid #e9ecef;
      border-radius: 6px;
      font-size: 0.85rem;
      transition: all 0.2s ease;
      margin-bottom: 0;

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
      }
    }
  }

  .logo-preview {
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 2px dashed #e9ecef;
    text-align: center;
    transition: all 0.2s ease;

    &:hover {
      border-color: rgba(var(--primary-rgb), 0.3);
    }

    img {
      max-width: 100%;
      max-height: 120px;
      border-radius: 6px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .layout-designer {
    background: #e5e7eb;
    padding: 1.5rem;
    overflow-y: auto;
  }

  .designer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h3 {
      margin: 0;
      color: var(--black);
    }
  }

  .a4-canvas {
    width: 794px;
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
    min-height: 1123px;
    padding: 40px;
    box-sizing: border-box;
  }

  .layout-row {
    border-bottom: 1px dashed #d1d5db;
    position: relative;

    &:last-child {
      border-bottom: none;
    }

    .row-columns {
      display: grid;
      gap: 1px;
      background: #e5e7eb;
      min-height: 60px;
    }
  }

  .column-slot {
    background: white;
    position: relative;
    min-height: 60px;
    border: 2px dashed transparent;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 4px;

    &:hover {
      border-color: #d1d5db;
    }

    &.occupied {
      border-color: transparent;
    }
  }

  .empty-slot {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    color: var(--grey);
    font-size: 0.9rem;
    font-style: italic;
  }

  .drag-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    background: var(--primary-fade);
    border: 2px dashed var(--primary);
    color: var(--primary);
    font-weight: 500;
    font-size: 0.9rem;
    animation: pulse 1.5s ease-in-out infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .placed-component {
    position: relative;
    min-height: 50px;
    flex: 1;
    border: 2px solid transparent;
    border-radius: 4px;
    transition: all 0.2s;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      box-shadow: inset 0 0 0 2px var(--primary-fade);
      border-color: var(--primary-fade);
    }

    &.selected {
      box-shadow: inset 0 0 0 2px var(--primary);
      border-color: var(--primary);
      background: rgba(var(--primary-rgb), 0.05);
    }

    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.25rem 0.5rem 0.2rem 0;
      // background: #eee;
      // border-bottom: 1px solid var(--border);
      opacity: 0;
      transition: opacity 0.2s;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      height: 20px;
      z-index: 100;

      .remove-component {
        width: 18px;
        height: 18px;
        background: #ef4444;
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: #dc2626;
        }
      }
    }

    &:hover .component-header {
      opacity: 1;
    }

    .component-content {
      padding: 0.75rem;
      flex: 1;
      overflow: hidden;
    }
  }

  .title-component {
    font-weight: bold;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .logo-component {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }

    .logo-placeholder {
      background: #f3f4f6;
      border: 2px dashed #d1d5db;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: #9ca3af;
      font-size: 0.9rem;
    }
  }

  .text-component {
    line-height: 1.4;
    overflow: hidden;
  }

  .fields-component {
    .field-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.25rem;

      .field-label {
        font-weight: 500;
      }

      .field-value {
        margin-left: 1rem;
      }
    }
  }

  .table-component {
    font-size: 0.8rem;

    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 1rem;

      th, td {
        padding: 0.5rem;
        text-align: left;
        border: 1px solid;
      }

      th {
        font-weight: 600;
      }
    }

    .totals-section {
      margin-left: auto;
      width: 200px;

      .total-row {
        display: flex;
        justify-content: space-between;
        padding: 0.25rem 0;

        &.total {
          font-weight: bold;
          border-top: 1px solid #e5e7eb;
          padding-top: 0.5rem;
        }
      }
    }
  }
</style>
