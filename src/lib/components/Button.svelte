<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  // Props
  export let type: 'button' | 'submit' | 'reset' = 'button';
  export let variant: 'primary' | 'secondary' | 'tertiary' | 'danger' = 'primary';
  export let size: 'default' | 'small' | 'medium' = 'default';
  export let disabled = false;
  export let icon: any = null; // Pass an SVG component, function, or object with render method

  // Event handling
  const dispatch = createEventDispatcher<{
    click: MouseEvent;
  }>();

  function handleClick(event: MouseEvent) {
    if (!disabled) {
      dispatch('click', event);
    }
  }
</script>

<button
  {type}
  class="button {variant} {size}"
  on:click={handleClick}
  {disabled}
  {...$$restProps}
>
  {#if icon}
    <span class="icon">
      {@html typeof icon === 'function' ? icon() : typeof icon === 'string' ? icon : typeof icon === 'object' && icon.render ? icon.render() : ''}
    </span>
  {/if}
  <slot />
</button>

<style lang="less">
  /* Define CSS variables locally if they're not available globally */
  :global(:root) {
    --br: 10px;
    --primary: #3730a3;
    --secondary: #4f46e5;
    --secondary-fade: rgba(79, 70, 229, 0.1);
    --secondary-fade2: rgba(79, 70, 229, 0.2);
    --white-on-dark: #fff;
    --black: #222;
    --white: #fff;
  }

  .button {
    border-radius: calc(var(--br) / 1.5);
    background: var(--primary);
    padding: 12px 20px;
    color: var(--white-on-dark);
    display: inline-flex;
    cursor: pointer;
    transition: 0.1s all ease-out;
    font-size: 16px;
    font-weight: 500;
    align-items: center;
    white-space: nowrap;
    border: none;

    :global(svg) {
      margin-right: 10px;

      :global(path) {
        fill: white;
      }
    }

    &:hover:not(:disabled) {
      background: var(--secondary);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.secondary {
      background: var(--secondary-fade);
      color: var(--black);

      :global(svg) {
        margin-right: 10px;
      }

      &:hover:not(:disabled) {
        background: var(--secondary-fade2);

        :global(svg) {
          :global(path) {
            stroke: var(--white);
            fill: white;
          }
        }
      }
    }

    &.tertiary {
      background: transparent;
      color: var(--primary);

      &:hover:not(:disabled) {
        background: var(--secondary-fade);
      }

      :global(svg) {
        :global(path) {
          fill: var(--primary);
        }
      }
    }

    &.small {
      font-size: 10px;
      padding: 5px 10px;
    }

    &.medium {
      font-size: 14px;
      font-weight: 600;
      padding: 10px 15px;
    }
  }
</style>
