<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { contactStore, customers } from '$lib/stores/customerStore';
  import type { Contact } from '$lib/api/contacts';

  const dispatch = createEventDispatcher();

  export let customerId: string = '';
  export let customerSearch: string = '';
  export let hasError: boolean = false; // Prop to indicate validation error
  export let errorMessage: string = ''; // Error message to display

  let showCustomerDropdown = false;
  let highlightedIndex = -1;

  // Helper function to filter customers
  function filterCustomers(searchTerm: string): Contact[] {
    if (!searchTerm.trim()) {
      return $customers;
    }
    const term = searchTerm.toLowerCase();
    return $customers.filter(customer =>
      customer.fullName.toLowerCase().includes(term) ||
      (customer.companyName && customer.companyName.toLowerCase().includes(term)) ||
      customer.emails.some(email => email.email.toLowerCase().includes(term))
    );
  }

  function handleCustomerSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    customerSearch = target.value;
    showCustomerDropdown = true;
    highlightedIndex = -1;
  }

  function selectCustomer(customer: Contact) {
    customerId = customer.id;
    customerSearch = customer.companyName || customer.fullName;
    showCustomerDropdown = false;
    dispatch('selectcustomer', customer.id);
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (!showCustomerDropdown) return;

    const filteredCustomers = filterCustomers(customerSearch);

    if (event.key === 'ArrowDown') {
      event.preventDefault();
      highlightedIndex = Math.min(highlightedIndex + 1, filteredCustomers.length - 1);
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      highlightedIndex = Math.max(highlightedIndex - 1, -1);
    } else if (event.key === 'Enter' && highlightedIndex >= 0) {
      event.preventDefault();
      selectCustomer(filteredCustomers[highlightedIndex]);
    } else if (event.key === 'Escape') {
      showCustomerDropdown = false;
    }
  }

  function handleInputBlur() {
    // Small delay to allow click events to fire on dropdown items
    setTimeout(() => {
      showCustomerDropdown = false;
    }, 200);
  }

  // Reactive statement for filtered customers
  $: filteredCustomers = filterCustomers(customerSearch);
</script>

<div class="form-group customer-search">
  <label for="customerSearch">Customer</label>
  <div class="search-container" class:has-error={hasError}>
    <input
      type="text"
      id="customerSearch"
      bind:value={customerSearch}
      on:input={handleCustomerSearch}
      on:keydown={handleKeyDown}
      on:blur={handleInputBlur}
      on:focus={() => showCustomerDropdown = true}
      placeholder="Search customers..."
      autocomplete="off"
      class="search-input"
    />
    <input type="hidden" bind:value={customerId} name="customerId" />
    {#if showCustomerDropdown && filteredCustomers.length > 0}
      <ul class="dropdown-menu">
        {#each filteredCustomers as customer, i}
          <li
            class:highlighted={i === highlightedIndex}
            on:mousedown|preventDefault={() => selectCustomer(customer)}
          >
            <div class="customer-option">
              <div class="customer-name">{customer.companyName || customer.fullName}</div>
              {#if customer.emails.length > 0}
                <div class="customer-email">{customer.emails.find(e => e.isPrimary)?.email || customer.emails[0].email}</div>
              {/if}
            </div>
          </li>
        {/each}
      </ul>
    {/if}
  </div>

  <!-- Error message -->
  {#if errorMessage}
    <div class="error-message">{errorMessage}</div>
  {/if}
</div>

<style lang="less">
  .form-group {
    margin-bottom: 20px;
    position: relative;

    &.customer-search {
      .search-container {
        position: relative;
        width: 100%;

        &.has-error {
          .search-input {
            border-color: var(--error);
          }
        }
      }

      .search-input {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border);
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        background-color: white;
        color: var(--text);

        &::placeholder {
          color: var(--grey-light);
        }

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
        }

        &:disabled {
          background-color: var(--bg);
          cursor: not-allowed;
        }
      }

      .dropdown-menu {
        position: absolute;
        top: calc(100% + 4px);
        left: 0;
        right: 0;
        max-height: 300px;
        overflow-y: auto;
        margin: 0;
        padding: 6px 0;
        background: white;
        border: 1px solid var(--border);
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);;
        z-index: 1000;
        list-style: none;
        animation: fadeIn 0.15s ease-out;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: var(--bg);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--grey-light);
          border-radius: 3px;
        }

        li {
          padding: 0;
          margin: 0;
          cursor: pointer;
          transition: background-color 0.1s ease;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary);
            opacity: 0;
            transition: opacity 0.1s ease;
            pointer-events: none;
          }

          &:hover::before {
            opacity: 0.05;
          }

          &.highlighted {
            background-color: var(--bg-hover);
            &::before {
              opacity: 0.1;
            }
          }
        }

        .customer-option {
          padding: 10px 16px;
          position: relative;
          z-index: 1;
        }

        .customer-name {
          font-weight: 500;
          color: var(--text);
          margin-bottom: 2px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .customer-email {
          font-size: 12px;
          color: var(--grey);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .no-results {
          padding: 12px 16px;
          color: var(--grey);
          font-size: 14px;
          text-align: center;
        }
      }
    }
  }

  .error-message {
    color: var(--error);
    font-size: 12px;
    margin-top: 5px;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>