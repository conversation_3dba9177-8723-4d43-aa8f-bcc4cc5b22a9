<script lang="ts">
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';

  // Example icon function
  const PlusIcon = () => `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
  `;

  function handleClick() {
    console.log('Button clicked!');
  }
</script>

<div class="button-example-page">
  <PageHeader title="Button Components">
    <svelte:fragment slot="actions">
      <Button variant="secondary" on:click={handleClick}>Back to Components</Button>
    </svelte:fragment>
  </PageHeader>
  
  <div class="content-container">

  <section>
    <h2>Variants</h2>
    <div class="button-row">
      <Button on:click={handleClick}>Primary Button</Button>
      <Button variant="secondary" on:click={handleClick}>Secondary Button</Button>
      <Button variant="tertiary" on:click={handleClick}>Tertiary Button</Button>
    </div>
  </section>

  <section>
    <h2>Sizes</h2>
    <div class="button-row">
      <Button size="small" on:click={handleClick}>Small Button</Button>
      <Button size="medium" on:click={handleClick}>Medium Button</Button>
      <Button on:click={handleClick}>Default Size</Button>
    </div>
  </section>

  <section>
    <h2>With Icons</h2>
    <div class="button-row">
      <Button icon={PlusIcon} on:click={handleClick}>Add Item</Button>
      <Button variant="secondary" icon={PlusIcon} on:click={handleClick}>Add Item</Button>
      <Button variant="tertiary" icon={PlusIcon} on:click={handleClick}>Add Item</Button>
    </div>
  </section>

  <section>
    <h2>Disabled State</h2>
    <div class="button-row">
      <Button disabled>Disabled Primary</Button>
      <Button variant="secondary" disabled>Disabled Secondary</Button>
      <Button variant="tertiary" disabled>Disabled Tertiary</Button>
    </div>
  </section>

  <section>
    <h2>Form Submission</h2>
    <form on:submit|preventDefault={() => alert('Form submitted!')}>
      <div class="form-row">
        <input type="text" placeholder="Enter some text..." />
        <Button type="submit">Submit Form</Button>
      </div>
    </form>
  </section>
  </div>
</div>

<style lang="less">
  .button-example-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem 2rem;
    
    .content-container {
      max-width: 100%;
      margin: 0 auto;
    }
    
    section {
      background: var(--bg);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      
      h2 {
        color: var(--text-primary);
        margin-top: 0;
        margin-bottom: 1.5rem;
        font-size: 1.25rem;
        font-weight: 600;
      }
    }
    
    .button-row {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      margin-bottom: 1.5rem;
      align-items: center;
    }
    
    .example-form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      max-width: 400px;
      margin-top: 1rem;
      padding: 1.5rem;
      background: var(--bg-secondary);
      border-radius: 8px;
      
      .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        label {
          font-weight: 500;
          color: var(--text-secondary);
          font-size: 0.875rem;
        }

        input, 
        select {
          padding: 0.5rem 0.75rem;
          border: 1px solid var(--border);
          border-radius: 4px;
          font-size: 0.9375rem;
          background: var(--bg);
          color: var(--text-primary);
          transition: border-color 0.2s, box-shadow 0.2s;
          
          &:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
          }
        }
      }
      
      .button-row {
        margin-top: 0.5rem;
      }
    }
  }
</style>