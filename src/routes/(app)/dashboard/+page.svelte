<script lang="ts">
  import { onMount } from 'svelte'; // Import onMount
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';

  let totalCustomers: number | null = null;
  let isLoadingCustomers = true;
  let customerError: string | null = null;

  async function fetchTotalCustomers() {
    isLoadingCustomers = true;
    customerError = null;
    try {
      const payload = {
        pageNumber: 1, // Requesting page 1
        pageSize: 1    // Requesting only 1 item to be light, hoping for a totalCount metadata
      };
      const response = await fetch('https://app-ejp-api.azurewebsites.net/customer/ListCustomersGrid', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const responseJson = await response.json(); // Expected: {success: boolean, data: any, totalCount?: number, errorMessage?: string}
      console.log('Customer count API response:', responseJson);

      if (responseJson.success) {
        // Option 1: Check for totalCount at the root of the responseJson (alongside 'data', 'success')
        if (typeof responseJson.totalCount === 'number') {
          totalCustomers = responseJson.totalCount;
        }
        // Option 2: Check if responseJson.data is an object and has totalCount (e.g., { items: [], totalCount: X })
        else if (responseJson.data && typeof responseJson.data.totalCount === 'number') {
          totalCustomers = responseJson.data.totalCount;
        }
        // Option 3: If responseJson.data is an array (as seen before {success: bool, data: Array[]})
        // and no totalCount is found, then this endpoint doesn't give total count with this query.
        // We cannot assume data.length is the total if API paginates.
        else if (Array.isArray(responseJson.data)) {
            totalCustomers = null; // Explicitly set to null to indicate not found
            console.warn('Total customer count not found in API response. The ListCustomersGrid endpoint might not provide a total system count with this query, or the response structure is different than expected for a total count.');
        } else {
          totalCustomers = null; // Fallback if structure is unexpected
          console.warn('Unexpected API response structure for customer count.');
        }
      } else {
        customerError = responseJson.errorMessage || 'Failed to retrieve customer count.';
        totalCustomers = null;
      }
    } catch (err) {
      console.error('Error fetching total customers:', err);
      customerError = err instanceof Error ? err.message : 'An unknown error occurred';
      totalCustomers = null;
    } finally {
      isLoadingCustomers = false;
    }
  }

  onMount(() => {
    fetchTotalCustomers();
  });
</script>

<svelte:head>
  <title>Dashboard</title>
</svelte:head>

<div class="dashboard">
  <PageHeader title="Dashboard">
    <svelte:fragment slot="actions">
      <!-- Actions slot for any future dashboard-specific buttons -->
    </svelte:fragment>
  </PageHeader>

  <main class="dashboard-content">
    <div class="dashboard-card">
      <h2>Welcome to your Dashboard</h2>
      <p>This is where you can see an overview of your application's activity.</p>
    </div>

    <div class="dashboard-card">
      <h3>Quick Stats</h3>
      {#if isLoadingCustomers}
        <LoadingSpinner message="Loading customer count..." size="small" />
      {:else if customerError}
        <p style="color: red;">Error: {customerError}</p>
      {:else if totalCustomers !== null}
        <p>Total Customers: <strong>{totalCustomers}</strong></p>
      {:else}
        <p>Total customer count not available.</p>
      {/if}
    </div>
  </main>
</div>

<style lang="less">
  .dashboard-content {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .dashboard-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
  }

  h2 {
    color: #333;
    margin-top: 0;
    margin-bottom: 1.5rem;
  }
</style>
