<script context="module" lang="ts">
  // Define types directly in the component for now
  export interface CustomerGridItem {
    id: string; // Or number, depending on your API
    name: string;
    email?: string;
    phone?: string;
    // Add other properties that come from your API for the grid
  }

  export interface SearchFieldConfig {
    displayName: string;
    queryKey: string;
    currentQuery: string;
  }

  export interface HeaderConfig {
    text: string;
    key: keyof CustomerGridItem | 'actions'; // Use keyof CustomerGridItem for sortable keys
    sortable: boolean;
  }

  export interface SortState {
    key: keyof CustomerGridItem | string; // Allow string for non-CustomerGridItem keys like 'actions'
    direction: 'ascending' | 'descending' | '';
  }
</script>

<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { customerStore } from '$lib/stores/customerStore'; // Placeholder, might still cause issues if store is not functional
  import Modal from '$lib/components/Modal.svelte';
  import ConfirmDelete from '$lib/components/ConfirmDelete.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import Button from '$lib/components/Button.svelte';
  import Grid from '$lib/components/Grid.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';

  let customers: CustomerGridItem[] = [];
  let isLoading = true;
  let error: string | null = null;
  let showDeleteModal = false;
  let customerToDelete: CustomerGridItem | null = null;

  let currentSort: SortState = { key: 'name', direction: 'ascending' };

  // New state for pagination, search, and items per page
  let currentPage = 1;
  let itemsPerPage = 10; // Default items per page
  let totalCustomerItems = 0;
  let customerSearchFields: SearchFieldConfig[] = [
    { displayName: 'Name', queryKey: 'name', currentQuery: '' },
    { displayName: 'Email', queryKey: 'email', currentQuery: '' }
    // Add more search fields if needed, e.g., for phone
  ];
  let currentSearchQueries: Record<string, string> = {};

  const customerTableHeaders: HeaderConfig[] = [
    { text: 'Name', key: 'name', sortable: true },
    { text: 'Email', key: 'email', sortable: true },
    { text: 'Phone', key: 'phone', sortable: true },
    { text: 'Actions', key: 'actions', sortable: false }
  ];

  async function loadCustomers() {
    isLoading = true;
    error = null;
    try {
      const payload = {
        pageNumber: currentPage,
        pageSize: itemsPerPage,
        sortField: currentSort.key || 'name', // Default sort field
        sortOrder: currentSort.direction || 'ascending', // Default sort order
        searchFilters: currentSearchQueries
      };
      console.log('Sending payload:', JSON.stringify(payload, null, 2)); // Log the payload

      const response = await fetch('https://app-ejp-api.azurewebsites.net/customer/ListCustomersGrid', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      console.log('API Response Status:', response.status); // Log response status
      // console.log('API Response Headers:', Object.fromEntries(response.headers.entries())); // Optional: log headers

      if (!response.ok) {
        let errorData = { message: `HTTP error! status: ${response.status}` };
        try {
          errorData = await response.json();
        } catch (e) {
          // If response.json() fails, use the status text or default message
          errorData.message = response.statusText || `HTTP error! status: ${response.status}`;
        }
        console.error('API Error Data:', errorData);
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      console.log('API Data Received:', data); // Log the data received

      if (data && data.success) {
        customers = data.data || []; // Assign customers from data.data
        totalCustomerItems = data.data ? data.data.length : 0; // Get count from the length of data.data
        // If the API provides a total count for pagination elsewhere in the response, adjust totalCustomerItems accordingly.
      } else {
        customers = [];
        totalCustomerItems = 0;
        // Optionally, set an error message if data.success is false
        if (data && data.errorMessage) {
          error = data.errorMessage;
          console.error('API returned an error:', data.errorMessage);
        }
      }

      // If current page has no items after deletion/filtering, and it's not the first page, try loading previous page.
      if (customers.length === 0 && currentPage > 1 && totalCustomerItems > 0) {
        currentPage = Math.max(1, currentPage -1); // Go to previous page or first page
        loadCustomers(); // Reload data for the new page
        return; // Exit current execution to avoid setting isLoading to false too early
      }

    } catch (e: any) {
      console.error('Error loading customers:', e);
      error = e.message || 'An unknown error occurred while fetching customers.';
      customers = []; // Clear customers on error
      totalCustomerItems = 0;
    } finally {
      isLoading = false;
    }
  }

  function handleSort(headerKey: string) {
    if (currentSort.key === headerKey) {
      currentSort.direction = currentSort.direction === 'ascending' ? 'descending' : 'ascending';
    } else {
      currentSort.key = headerKey;
      currentSort.direction = 'ascending';
    }
    currentPage = 1; // Reset to first page on sort
    loadCustomers();
  }

  // New handlers for Grid events
  function handleCustomerSearch(searchQueries: Record<string, string>) {
    currentSearchQueries = searchQueries;
    currentPage = 1; // Reset to first page on new search
    loadCustomers();
  }

  function handleCustomerItemsPerPageChange(newItemsPerPage: number) {
    itemsPerPage = newItemsPerPage;
    currentPage = 1; // Reset to first page on items per page change
    loadCustomers();
  }

  function handleCustomerPageChange(newPage: number) {
    currentPage = newPage;
    loadCustomers();
  }

  function viewCustomerDetails(customerId: string) {
    customerStore.set({ id: customerId, name: '', email: '', phone: '' }); // Basic info for now
    goto(`/customers/${customerId}`);
  }

  // Function to set the customer to be deleted and show the modal
  function initiateDelete(customer: CustomerGridItem) {
    customerToDelete = customer;
    showDeleteModal = true;
  }

  async function confirmDelete() {
    if (!customerToDelete) return;
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/customers/${customerToDelete.id}`, { method: 'DELETE' });
      // if (!response.ok) throw new Error('Failed to delete customer');
      console.log('Simulating delete for:', customerToDelete.name);
      addToast({ message: `Customer "${customerToDelete.name}" deleted successfully. (Simulated)`, type: 'success' });

      // Optimistically remove from list or reload
      // customers = customers.filter(c => c.id !== customerToDelete!.id);
      // totalCustomerItems--; // Decrement total count
      // OR reload to get fresh data and counts:
      loadCustomers();

    } catch (e: any) {
      addToast({ message: e.message || 'Error deleting customer.', type: 'error' });
    } finally {
      showDeleteModal = false;
      customerToDelete = null;
    }
  }

  function addCustomer() {
    goto('/customers/new');
  }

  function editCustomer(customer: CustomerGridItem) {
    if (customer && customer.id) {
      goto(`/customers/${customer.id}/edit`);
    } else {
      console.error('editCustomer called with invalid customer data:', customer);
      addToast({ type: 'error', message: 'Cannot edit customer: ID missing.' });
    }
  }

  onMount(() => {
    loadCustomers(); // Initial load with default pagination/sort
  });

</script>

<svelte:head>
  <title>Customers</title>
</svelte:head>

<div class="customers-page">
  <PageHeader title="Customers">
    <svelte:fragment slot="actions">
      <Button on:click={addCustomer} variant="primary" type="button">Add Customer</Button>
    </svelte:fragment>
  </PageHeader>

  <main>

  {#if isLoading}
    <LoadingSpinner message="Loading customers..." />
  {:else if error}
    <p class="error-message">Error: {error} <Button on:click={loadCustomers} type="button">Retry</Button></p>
  {:else}
    <Grid
      headers={customerTableHeaders}
      dataRows={customers}
      emptyMessage="No customers found."
      onHeaderClick={handleSort}
      {currentSort}
      searchFields={customerSearchFields}
      {itemsPerPage}
      itemsPerPageOptions={[10, 25, 50]}
      {currentPage}
      totalItems={totalCustomerItems}
      onSearch={handleCustomerSearch}
      onItemsPerPageChange={handleCustomerItemsPerPageChange}
      onPageChange={handleCustomerPageChange}
    >
      <svelte:fragment slot="cell" let:row let:headerKey let:value>
        {#if headerKey === 'actions'}
          <div class="action-buttons">
            <Button on:click={() => editCustomer(row as CustomerGridItem)} variant="secondary" size="small" type="button">Edit</Button>
            <Button on:click={() => viewCustomerDetails((row as CustomerGridItem).id)} variant="secondary" size="small" type="button">View Details</Button>
            <Button on:click={() => initiateDelete(row as CustomerGridItem)} variant="tertiary" size="small" type="button">Delete</Button>
          </div>
        {:else}
          {value}
        {/if}
      </svelte:fragment>
    </Grid>
  {/if}
  </main>
</div>

{#if showDeleteModal && customerToDelete}
  <Modal title="Confirm Delete" on:close={() => showDeleteModal = false}>
    <ConfirmDelete
      itemName={customerToDelete.name}
      on:confirm={confirmDelete}
      on:cancel={() => showDeleteModal = false}
    />
  </Modal>
{/if}

<style lang="less">
  .error-message {
    color: var(--error);
    margin-bottom: 10px;
  }
  .action-buttons {
    display: flex;
    gap: 8px; // Creates space between buttons
    justify-content: flex-start; // Align buttons to the start of the cell
  }
</style>
