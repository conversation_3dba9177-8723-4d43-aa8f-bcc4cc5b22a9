<script lang="ts">
	import VerticalMenu from '$lib/components/VerticalMenu.svelte';
	import GeneralSettings from '$lib/components/settings/GeneralSettings.svelte';
	import BrandSettings from '$lib/components/settings/BrandSettings.svelte';

	let activeSetting = 'general'; // Default to 'general'

	const menuItems = [
		{ id: 'general', label: 'General' },
		{ id: 'brand', label: 'Brand' }
	];

	function handleMenuSelect(itemId: string) {
		activeSetting = itemId;
	}
</script>

<div class="settings-page-container">
	<h1>Settings</h1>
	<div class="settings-layout">
		<aside class="settings-menu">
			<VerticalMenu items={menuItems} currentItem={activeSetting} onSelect={handleMenuSelect} />
		</aside>
		<main class="settings-content">
			{#if activeSetting === 'general'}
				<GeneralSettings />
			{:else if activeSetting === 'brand'}
				<BrandSettings />
			{/if}
		</main>
	</div>
</div>

<style>
	.settings-page-container {
		padding: 20px;
		height: 100%; /* Ensure it takes available height */
		display: flex;
		flex-direction: column;
	}

	.settings-page-container h1 {
		margin-bottom: 20px;
		font-size: 1.8rem;
	}

	.settings-layout {
		display: flex;
		gap: 24px; 
		flex-grow: 1; /* Allows content to fill space */
		overflow: hidden; /* Prevents layout issues if content overflows */
	}

	.settings-menu {
		flex: 0 0 220px; /* Fixed width for the menu */
		background-color: #f9f9f9; /* Light background for menu area */
		border-radius: 8px;
		padding: 10px;
		box-shadow: 0 2px 4px rgba(0,0,0,0.05);
	}

	.settings-content {
		flex-grow: 1;
		background-color: #ffffff;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0,0,0,0.05);
		overflow-y: auto; /* Allow content to scroll if it's too long */
	}
</style>
