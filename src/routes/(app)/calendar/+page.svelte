<script lang="ts">
  import { onMount, tick } from 'svelte';
  import { flip } from 'svelte/animate';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { jobs, jobStore } from '$lib/stores/jobStore';
  import { staff, staffStore } from '$lib/stores/staffStore';
  import { customers } from '$lib/stores/customerStore';
  import { dndzone } from 'svelte-dnd-action';
  import {
    getCalendarEvents,
    createCalendarEvent,
    updateCalendarEvent,
    deleteCalendarEvent,
    type CalendarEvent,
    type CalendarStaffAssignment
  } from '$lib/api/calendar';

  let loading = true;
  let calendarEvents: CalendarEvent[] = [];
  let viewMode: 'day' | 'week' | 'month' = 'month';
  let selectedStaff = 'all';
  let showEventModal = false;
  let editingEvent: CalendarEvent | null = null;

  // Calendar navigation
  let displayDate = new Date();

  // Event form data
  let eventFormData = {
    title: '',
    description: '',
    startDate: '',
    startTime: '',
    endDate: '',
    endTime: '',
    jobId: '',
    customerId: '',
    assignedStaffIds: [] as string[],
    priority: 'Medium' as 'Low' | 'Medium' | 'High' | 'Urgent',
    allDay: false,
    status: 'Scheduled' as 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled'
  };

  let errors: Record<string, string> = {};
  let formSubmitted = false;

  // Drag and drop state management - simplified approach
  let localEventsByDate: Record<string, CalendarEvent[]> = {};

  onMount(async () => {
    // Create a test event if none exist (for testing drag and drop)
    const existingEvents = localStorage.getItem('ejp_calendar_events');
    if (!existingEvents || JSON.parse(existingEvents).length === 0) {
      const testEvent = {
        id: 'test-event-1',
        title: 'Test Drag Event',
        description: 'A test event for drag and drop functionality',
        startDateTime: new Date().toISOString(),
        endDateTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours later
        allDay: false,
        assignedStaff: [],
        customerId: 'test-customer',
        customerName: 'Test Customer',
        status: 'Scheduled',
        priority: 'Medium',
        jobId: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      localStorage.setItem('ejp_calendar_events', JSON.stringify([testEvent]));
    }
    
    await loadData();
  });

  async function loadData() {
    loading = true;
    try {
      await Promise.all([
        jobStore.loadJobs(),
        staffStore.loadStaff(),
        loadCalendarEvents()
      ]);
    } catch (error) {
      console.error('Error loading calendar data:', error);
      addToast({ message: 'Failed to load calendar data', type: 'error' });
    } finally {
      loading = false;
    }
  }

  async function loadCalendarEvents() {
    try {
      // Get events for a wide date range to show in calendar
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 6);
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + 6);

      const events = await getCalendarEvents(
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      );

      // Force reactivity by creating a new array reference
      calendarEvents = [...events];
      console.log('Loaded calendar events:', calendarEvents.length, calendarEvents);
    } catch (error) {
      console.error('Error loading calendar events:', error);
      throw error;
    }
  }

  // Calendar navigation functions
  function navigatePrevious() {
    const newDate = new Date(displayDate);
    switch (viewMode) {
      case 'day':
        newDate.setDate(newDate.getDate() - 1);
        break;
      case 'week':
        newDate.setDate(newDate.getDate() - 7);
        break;
      case 'month':
        newDate.setMonth(newDate.getMonth() - 1);
        break;
    }
    displayDate = newDate;
  }

  function navigateNext() {
    const newDate = new Date(displayDate);
    switch (viewMode) {
      case 'day':
        newDate.setDate(newDate.getDate() + 1);
        break;
      case 'week':
        newDate.setDate(newDate.getDate() + 7);
        break;
      case 'month':
        newDate.setMonth(newDate.getMonth() + 1);
        break;
    }
    displayDate = newDate;
  }

  function navigateToday() {
    displayDate = new Date();
  }

  // Get events for display based on current view and filters
  function getFilteredEvents(): CalendarEvent[] {
    let filtered = calendarEvents;

    // Filter by staff if selected
    if (selectedStaff !== 'all') {
      filtered = filtered.filter(event =>
        event.assignedStaff.some(staff => staff.staffId === selectedStaff)
      );
    }

    // Filter by date range based on view
    const startOfPeriod = getStartOfPeriod(displayDate, viewMode);
    const endOfPeriod = getEndOfPeriod(displayDate, viewMode);

    filtered = filtered.filter(event => {
      const eventStart = new Date(event.startDateTime);
      const eventEnd = new Date(event.endDateTime);
      return eventStart <= endOfPeriod && eventEnd >= startOfPeriod;
    });

    return filtered;
  }

  function getStartOfPeriod(date: Date, view: string): Date {
    const result = new Date(date);
    switch (view) {
      case 'day':
        result.setHours(0, 0, 0, 0);
        break;
      case 'week':
        result.setDate(result.getDate() - result.getDay());
        result.setHours(0, 0, 0, 0);
        break;
      case 'month':
        result.setDate(1);
        result.setHours(0, 0, 0, 0);
        break;
    }
    return result;
  }

  function getEndOfPeriod(date: Date, view: string): Date {
    const result = new Date(date);
    switch (view) {
      case 'day':
        result.setHours(23, 59, 59, 999);
        break;
      case 'week':
        result.setDate(result.getDate() - result.getDay() + 6);
        result.setHours(23, 59, 59, 999);
        break;
      case 'month':
        result.setMonth(result.getMonth() + 1, 0);
        result.setHours(23, 59, 59, 999);
        break;
    }
    return result;
  }

  // Generate calendar grid for month view
  function generateMonthGrid(): Date[] {
    const firstDay = new Date(displayDate.getFullYear(), displayDate.getMonth(), 1);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());

    const dates: Date[] = [];
    const current = new Date(startDate);

    // Generate 6 weeks (42 days) to fill the calendar grid
    for (let i = 0; i < 42; i++) {
      dates.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    return dates;
  }

  // Get events for a specific date
  function getEventsForDate(date: Date): CalendarEvent[] {
    const dateStr = date.toDateString();
    return getFilteredEvents().filter(event => {
      const eventStart = new Date(event.startDateTime);
      const eventEnd = new Date(event.endDateTime);
      return eventStart.toDateString() === dateStr ||
             (eventStart <= date && eventEnd >= date);
    });
  }

  // Get week days for week view
  function getWeekDays(): Date[] {
    const startOfWeek = new Date(displayDate);
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());

    const days: Date[] = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(day.getDate() + i);
      days.push(day);
    }
    return days;
  }

  // Get time slots for week view
  function getTimeSlots(): string[] {
    const slots: string[] = [];
    for (let hour = 6; hour < 22; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`);
    }
    return slots;
  }

  // Get events for a specific time slot
  function getEventsForTimeSlot(date: Date, timeSlot: string): CalendarEvent[] {
    const [hour] = timeSlot.split(':').map(Number);
    const slotStart = new Date(date);
    slotStart.setHours(hour, 0, 0, 0);
    const slotEnd = new Date(slotStart);
    slotEnd.setHours(hour + 1, 0, 0, 0);

    return getEventsForDate(date).filter(event => {
      const eventStart = new Date(event.startDateTime);
      const eventEnd = new Date(event.endDateTime);

      // Check if event overlaps with this time slot
      return eventStart < slotEnd && eventEnd > slotStart;
    });
  }

  // Open create modal at specific time
  function openCreateModalAtTime(date: Date, timeSlot: string) {
    editingEvent = null;
    resetEventForm();

    const [hour] = timeSlot.split(':').map(Number);
    eventFormData.startDate = date.toISOString().split('T')[0];
    eventFormData.endDate = date.toISOString().split('T')[0];
    eventFormData.startTime = `${hour.toString().padStart(2, '0')}:00`;
    eventFormData.endTime = `${(hour + 1).toString().padStart(2, '0')}:00`;

    showEventModal = true;
  }

  // Sync local state with calendar events when they change
  $: {
    if (calendarEvents && viewMode === 'month' && monthGrid.length > 0) {
      const newLocalEvents: Record<string, CalendarEvent[]> = {};
      for (const date of monthGrid) {
        const dateKey = date.toDateString();
        const events = getEventsForDate(date);
        // Ensure all events have valid IDs for drag and drop
        newLocalEvents[dateKey] = events.filter(event => event && event.id);
      }
      localEventsByDate = newLocalEvents;
      console.log('Updated localEventsByDate for month view:', localEventsByDate);
    } else if (calendarEvents && viewMode === 'week') {
      const newLocalEvents: Record<string, CalendarEvent[]> = {};
      for (const date of getWeekDays()) {
        for (const timeSlot of getTimeSlots()) {
          const slotKey = `${date.toDateString()}-${timeSlot}`;
          const events = getEventsForTimeSlot(date, timeSlot);
          // Ensure all events have valid IDs for drag and drop
          newLocalEvents[slotKey] = events.filter(event => event && event.id);
        }
      }
      localEventsByDate = newLocalEvents;
      console.log('Updated localEventsByDate for week view:', localEventsByDate);
    }
  }

  // Drag and drop handlers for month view
  function handleDndConsider(dateKey: string, e: CustomEvent<{items: CalendarEvent[]}>) {
    console.log('DND Consider:', dateKey, e.detail.items);
    // Update the local state for smooth drag experience
    localEventsByDate = { 
      ...localEventsByDate,
      [dateKey]: e.detail.items
    };
  }

  async function handleDndFinalize(dateKey: string, targetDate: Date, e: CustomEvent<{items: CalendarEvent[]}>, timeSlot?: string) {
    const newItems = e.detail.items;
    
    // Update local state
    localEventsByDate = { 
      ...localEventsByDate,
      [dateKey]: newItems
    };
    
    // Find events that need to be moved to a different date or time
    for (const event of newItems) {
      const originalEvent = calendarEvents.find(ce => ce.id === event.id);
      if (originalEvent) {
        const originalDate = new Date(originalEvent.startDateTime).toDateString();
        const newDate = targetDate.toDateString();
        const originalStart = new Date(originalEvent.startDateTime);
        
        let needsUpdate = false;
        let newStart = new Date(originalStart);
        
        // Check if date changed
        if (originalDate !== newDate) {
          needsUpdate = true;
          newStart = new Date(targetDate);
          newStart.setHours(originalStart.getHours(), originalStart.getMinutes(), 0, 0);
        }
        
        // Check if time slot changed (for week view)
        if (timeSlot) {
          const [hour] = timeSlot.split(':').map(Number);
          if (originalStart.getHours() !== hour) {
            needsUpdate = true;
            newStart.setHours(hour, 0, 0, 0);
          }
        }
        
        if (needsUpdate) {
          try {
            // Calculate duration and new end time
            const originalEnd = new Date(originalEvent.endDateTime);
            const duration = originalEnd.getTime() - originalStart.getTime();
            const newEnd = new Date(newStart.getTime() + duration);

            // Update the event
            await updateCalendarEvent(originalEvent.id, {
              startDateTime: newStart.toISOString(),
              endDateTime: newEnd.toISOString()
            });

            addToast({
              message: `Event "${event.title}" moved successfully`,
              type: 'success'
            });
          } catch (error) {
            console.error('Error moving event:', error);
            addToast({
              message: 'Failed to move event',
              type: 'error'
            });
            // Reload to reset state
            await loadCalendarEvents();
            return;
          }
        }
      }
    }
    
    // Reload calendar events to get the updated data
    await loadCalendarEvents();
  }

  // Modal functions
  function openCreateModal(date?: Date) {
    editingEvent = null;
    resetEventForm();
    if (date) {
      eventFormData.startDate = date.toISOString().split('T')[0];
      eventFormData.endDate = date.toISOString().split('T')[0];
    }
    showEventModal = true;
  }

  function openEditModal(event: CalendarEvent) {
    editingEvent = event;
    populateEventForm(event);
    showEventModal = true;
  }

  function closeEventModal() {
    showEventModal = false;
    editingEvent = null;
    resetEventForm();
    errors = {};
    formSubmitted = false;
  }

  function resetEventForm() {
    eventFormData = {
      title: '',
      description: '',
      startDate: new Date().toISOString().split('T')[0],
      startTime: '09:00',
      endDate: new Date().toISOString().split('T')[0],
      endTime: '17:00',
      jobId: '',
      customerId: '',
      assignedStaffIds: [],
      priority: 'Medium',
      allDay: false,
      status: 'Scheduled'
    };
  }

  function populateEventForm(event: CalendarEvent) {
    const startDate = new Date(event.startDateTime);
    const endDate = new Date(event.endDateTime);

    eventFormData = {
      title: event.title,
      description: event.description || '',
      startDate: startDate.toISOString().split('T')[0],
      startTime: startDate.toTimeString().slice(0, 5),
      endDate: endDate.toISOString().split('T')[0],
      endTime: endDate.toTimeString().slice(0, 5),
      jobId: event.jobId || '',
      customerId: event.customerId,
      assignedStaffIds: event.assignedStaff.map(staff => staff.staffId),
      priority: event.priority,
      allDay: event.allDay,
      status: event.status
    };
  }

  function validateEventForm(): boolean {
    errors = {};

    if (!eventFormData.title.trim()) {
      errors.title = 'Title is required';
    }

    if (!eventFormData.startDate) {
      errors.startDate = 'Start date is required';
    }

    if (!eventFormData.endDate) {
      errors.endDate = 'End date is required';
    }

    if (!eventFormData.startTime) {
      errors.startTime = 'Start time is required';
    }

    if (!eventFormData.endTime) {
      errors.endTime = 'End time is required';
    }

    if (!eventFormData.customerId) {
      errors.customerId = 'Customer is required';
    }

    // Validate that end date/time is after start date/time
    if (eventFormData.startDate && eventFormData.endDate &&
        eventFormData.startTime && eventFormData.endTime) {
      const startDateTime = new Date(`${eventFormData.startDate}T${eventFormData.startTime}`);
      const endDateTime = new Date(`${eventFormData.endDate}T${eventFormData.endTime}`);

      if (endDateTime <= startDateTime) {
        errors.endTime = 'End time must be after start time';
      }
    }

    return Object.keys(errors).length === 0;
  }

  async function handleEventSubmit() {
    formSubmitted = true;

    if (!validateEventForm()) {
      addToast({
        message: 'Please fix the errors in the form before submitting',
        type: 'error'
      });
      return;
    }

    try {
      const startDateTime = new Date(`${eventFormData.startDate}T${eventFormData.startTime}`);
      const endDateTime = new Date(`${eventFormData.endDate}T${eventFormData.endTime}`);

      // Convert assignedStaffIds to assignedStaff format
      const assignedStaff: CalendarStaffAssignment[] = eventFormData.assignedStaffIds.map(staffId => {
        const staffMember = $staff.find((s: any) => s.id === staffId);
        return {
          staffId,
          staffName: staffMember ? staffMember.fullName : 'Unknown Staff',
          confirmed: false
        };
      });

      const eventData = {
        title: eventFormData.title,
        description: eventFormData.description,
        startDateTime: startDateTime.toISOString(),
        endDateTime: endDateTime.toISOString(),
        jobId: eventFormData.jobId || '',
        customerId: eventFormData.customerId,
        assignedStaff,
        priority: eventFormData.priority,
        allDay: eventFormData.allDay,
        status: eventFormData.status
      };

      if (editingEvent) {
        await updateCalendarEvent(editingEvent.id, eventData);
        addToast({
          message: 'Event updated successfully',
          type: 'success'
        });
      } else {
        await createCalendarEvent(eventData);
        addToast({
          message: 'Event created successfully',
          type: 'success'
        });
      }

      // Reload calendar events and force UI update
      await loadCalendarEvents();
      await tick(); // Ensure DOM updates are processed
      closeEventModal();
    } catch (error) {
      console.error('Error saving event:', error);
      addToast({
        message: error instanceof Error ? error.message : 'An unknown error occurred',
        type: 'error'
      });
    }
  }

  async function handleDeleteEvent(event: CalendarEvent) {
    if (window.confirm(`Are you sure you want to delete "${event.title}"?`)) {
      try {
        await deleteCalendarEvent(event.id);
        // Reload calendar events and force UI update
        await loadCalendarEvents();
        await tick(); // Ensure DOM updates are processed
        addToast({
          message: 'Event deleted successfully',
          type: 'success'
        });
      } catch (error) {
        console.error('Error deleting event:', error);
        addToast({
          message: 'Failed to delete event',
          type: 'error'
        });
      }
    }
  }

  // Utility functions
  function formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  function formatTime(dateTime: string): string {
    return new Date(dateTime).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  function getStaffName(staffId: string): string {
    const staffMember = $staff.find((s: any) => s.id === staffId);
    return staffMember ? staffMember.fullName : 'Unknown Staff';
  }

  function getJobTitle(jobId: string): string {
    const job = $jobs.find((j: any) => j.id === jobId);
    return job ? job.title : 'Unknown Job';
  }

  function getCustomerName(customerId: string): string {
    const customer = $customers.find((c: any) => c.id === customerId);
    return customer ? (customer.companyName || customer.fullName) : 'Unknown Customer';
  }

  function isToday(date: Date): boolean {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  function isCurrentMonth(date: Date): boolean {
    return date.getMonth() === displayDate.getMonth();
  }

  // Reactive statements
  $: filteredEvents = getFilteredEvents();
  $: monthGrid = viewMode === 'month' ? generateMonthGrid() : [];

  // Force reactivity when calendarEvents changes
  $: if (calendarEvents) {
    // This reactive statement ensures the UI updates when calendarEvents changes
    filteredEvents = getFilteredEvents();
  }

  // Format display title based on view mode
  $: displayTitle = (() => {
    switch (viewMode) {
      case 'day':
        return formatDate(displayDate);
      case 'week':
        const weekStart = new Date(displayDate);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 6);
        return `${weekStart.toLocaleDateString()} - ${weekEnd.toLocaleDateString()}`;
      case 'month':
        return displayDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
      default:
        return '';
    }
  })();
</script>

<svelte:head>
  <title>Job Calendar</title>
</svelte:head>

<div class="container">
  <PageHeader title="Job Calendar">
    <svelte:fragment slot="actions">
      <Button on:click={() => openCreateModal()} variant="primary">
        Add Event
      </Button>
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if loading}
      <div class="loading-container">
        <LoadingSpinner />
        <p>Loading calendar...</p>
      </div>
    {:else}
      <!-- Calendar Controls -->
      <div class="calendar-controls">
        <div class="navigation">
          <Button variant="secondary" size="small" on:click={navigatePrevious}>
            ←
          </Button>
          <Button variant="secondary" size="small" on:click={navigateToday}>
            Today
          </Button>
          <Button variant="secondary" size="small" on:click={navigateNext}>
            →
          </Button>
          <h2 class="calendar-title">{displayTitle}</h2>
        </div>

        <div class="view-controls">
          <div class="view-buttons">
            <Button
              variant={viewMode === 'day' ? 'primary' : 'secondary'}
              size="small"
              on:click={() => viewMode = 'day'}
            >
              Day
            </Button>
            <Button
              variant={viewMode === 'week' ? 'primary' : 'secondary'}
              size="small"
              on:click={() => viewMode = 'week'}
            >
              Week
            </Button>
            <Button
              variant={viewMode === 'month' ? 'primary' : 'secondary'}
              size="small"
              on:click={() => viewMode = 'month'}
            >
              Month
            </Button>
          </div>

          <div class="staff-filter">
            <label for="staff-filter">Staff:</label>
            <select id="staff-filter" bind:value={selectedStaff}>
              <option value="all">All Staff</option>
              {#each $staff.filter((s: any) => s.isActive) as staffMember}
                <option value={staffMember.id}>{staffMember.fullName}</option>
              {/each}
            </select>
          </div>
        </div>
      </div>

      <!-- Calendar Views -->
      <div class="calendar-container">
        {#if viewMode === 'month'}
          <div class="month-view">
            <div class="month-header">
              {#each ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] as day}
                <div class="day-header">{day}</div>
              {/each}
            </div>
            <div class="month-grid">
              {#each monthGrid as date}
                <div
                  class="day-cell"
                  class:today={isToday(date)}
                  class:other-month={!isCurrentMonth(date)}
                  on:click={() => openCreateModal(date)}
                  role="button"
                  tabindex="0"
                  use:dndzone={{
                    items: (localEventsByDate[date.toDateString()] || []).map(event => ({ ...event, id: event.id })),
                    flipDurationMs: 200,
                    dropTargetStyle: { outline: '2px dashed var(--primary)' }
                  }}
                  on:consider={(e) => handleDndConsider(date.toDateString(), e)}
                  on:finalize={(e) => handleDndFinalize(date.toDateString(), date, e)}
                >
                  <div class="day-number">{date.getDate()}</div>
                  <div class="day-events">
                    {#each localEventsByDate[date.toDateString()] || [] as event (event.id)}
                      <div
                        class="event-item draggable"
                        on:click|stopPropagation={() => openEditModal(event)}
                        role="button"
                        tabindex="0"
                        data-id={event.id}
                        animate:flip={{ duration: 200 }}
                      >
                        <span class="event-title">{event.title}</span>
                        <span class="event-time">{formatTime(event.startDateTime)}</span>
                      </div>
                    {/each}
                  </div>
                </div>
              {/each}
            </div>
          </div>
        {:else if viewMode === 'week'}
          <div class="week-view">
            <div class="week-header">
              <div class="time-column"></div>
              {#each getWeekDays() as date}
                <div class="day-header" class:today={isToday(date)}>
                  <div class="day-name">{date.toLocaleDateString('en-US', { weekday: 'short' })}</div>
                  <div class="day-number">{date.getDate()}</div>
                </div>
              {/each}
            </div>
            <div class="week-grid">
              <div class="time-column">
                {#each getTimeSlots() as timeSlot}
                  <div class="time-slot">{timeSlot}</div>
                {/each}
              </div>
              {#each getWeekDays() as date, dayIndex}
                <div class="day-column">
                  {#each getTimeSlots() as timeSlot, timeIndex}
                    <div
                      class="time-cell"
                      on:click={() => openCreateModalAtTime(date, timeSlot)}
                      role="button"
                      tabindex="0"
                      use:dndzone={{
                        items: (localEventsByDate[`${date.toDateString()}-${timeSlot}`] || []).map(event => ({ ...event, id: event.id })),
                        flipDurationMs: 200,
                        dropTargetStyle: { outline: '2px dashed var(--primary)' }
                      }}
                      on:consider={(e) => handleDndConsider(`${date.toDateString()}-${timeSlot}`, e)}
                                             on:finalize={(e) => handleDndFinalize(`${date.toDateString()}-${timeSlot}`, date, e, timeSlot)}
                    >
                      {#each localEventsByDate[`${date.toDateString()}-${timeSlot}`] || [] as event (event.id)}
                        <div
                          class="week-event draggable"
                          on:click|stopPropagation={() => openEditModal(event)}
                          role="button"
                          tabindex="0"
                          data-id={event.id}
                          animate:flip={{ duration: 200 }}
                        >
                          <div class="event-title">{event.title}</div>
                          <div class="event-time">{formatTime(event.startDateTime)}</div>
                        </div>
                      {/each}
                    </div>
                  {/each}
                </div>
              {/each}
            </div>
          </div>
        {:else if viewMode === 'day'}
          <div class="day-view">
            <h3>{formatDate(displayDate)}</h3>
            <div class="day-events">
              {#each getEventsForDate(displayDate) as event}
                <div class="event-card" on:click={() => openEditModal(event)} role="button" tabindex="0">
                  <div class="event-header">
                    <h4>{event.title}</h4>
                    <div class="event-actions">
                      <Button variant="tertiary" size="small" on:click={() => handleDeleteEvent(event)}>
                        Delete
                      </Button>
                    </div>
                  </div>
                  <div class="event-details">
                    <p><strong>Time:</strong> {formatTime(event.startDateTime)} - {formatTime(event.endDateTime)}</p>
                    <p><strong>Customer:</strong> {getCustomerName(event.customerId)}</p>
                    {#if event.description}
                      <p><strong>Description:</strong> {event.description}</p>
                    {/if}
                    {#if event.jobId}
                      <p><strong>Job:</strong> {getJobTitle(event.jobId)}</p>
                    {/if}
                    {#if event.assignedStaff.length > 0}
                      <p><strong>Staff:</strong> {event.assignedStaff.map(staff => staff.staffName).join(', ')}</p>
                    {/if}
                  </div>
                </div>
              {/each}
              {#if getEventsForDate(displayDate).length === 0}
                <div class="no-events">
                  <p>No events scheduled for this day.</p>
                  <Button on:click={() => openCreateModal(displayDate)}>Add Event</Button>
                </div>
              {/if}
            </div>
          </div>
        {/if}
      </div>
    {/if}
  </main>
</div>

<!-- Event Modal -->
{#if showEventModal}
  <div class="modal-overlay" on:click={closeEventModal}>
    <div class="modal-content" on:click|stopPropagation>
      <div class="modal-header">
        <h2>{editingEvent ? 'Edit Event' : 'Create Event'}</h2>
        <button class="close-button" on:click={closeEventModal}>&times;</button>
      </div>

      <div class="modal-body">
        <form on:submit|preventDefault={handleEventSubmit} class="event-form">
          <div class="form-group">
            <label for="title">Title *</label>
            <input
              type="text"
              id="title"
              bind:value={eventFormData.title}
              class:error={formSubmitted && errors.title}
              placeholder="Event title"
            />
            {#if formSubmitted && errors.title}
              <div class="error-message">{errors.title}</div>
            {/if}
          </div>

          <div class="form-group">
            <label for="customerId">Customer *</label>
            <select
              id="customerId"
              bind:value={eventFormData.customerId}
              class:error={formSubmitted && errors.customerId}
            >
              <option value="">Select a customer</option>
              {#each $customers as customer}
                <option value={customer.id}>{customer.companyName || customer.fullName}</option>
              {/each}
            </select>
            {#if formSubmitted && errors.customerId}
              <div class="error-message">{errors.customerId}</div>
            {/if}
          </div>

          <div class="form-group">
            <label for="description">Description</label>
            <textarea
              id="description"
              bind:value={eventFormData.description}
              rows="3"
              placeholder="Event description"
            ></textarea>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="startDate">Start Date *</label>
              <input
                type="date"
                id="startDate"
                bind:value={eventFormData.startDate}
                class:error={formSubmitted && errors.startDate}
              />
              {#if formSubmitted && errors.startDate}
                <div class="error-message">{errors.startDate}</div>
              {/if}
            </div>

            <div class="form-group">
              <label for="startTime">Start Time *</label>
              <input
                type="time"
                id="startTime"
                bind:value={eventFormData.startTime}
                class:error={formSubmitted && errors.startTime}
              />
              {#if formSubmitted && errors.startTime}
                <div class="error-message">{errors.startTime}</div>
              {/if}
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="endDate">End Date *</label>
              <input
                type="date"
                id="endDate"
                bind:value={eventFormData.endDate}
                class:error={formSubmitted && errors.endDate}
              />
              {#if formSubmitted && errors.endDate}
                <div class="error-message">{errors.endDate}</div>
              {/if}
            </div>

            <div class="form-group">
              <label for="endTime">End Time *</label>
              <input
                type="time"
                id="endTime"
                bind:value={eventFormData.endTime}
                class:error={formSubmitted && errors.endTime}
              />
              {#if formSubmitted && errors.endTime}
                <div class="error-message">{errors.endTime}</div>
              {/if}
            </div>
          </div>

          <div class="form-group">
            <label for="jobId">Related Job</label>
            <select id="jobId" bind:value={eventFormData.jobId}>
              <option value="">No related job</option>
              {#each $jobs as job}
                <option value={job.id}>{job.title} - {job.customerName}</option>
              {/each}
            </select>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="priority">Priority</label>
              <select id="priority" bind:value={eventFormData.priority}>
                <option value="Low">Low</option>
                <option value="Medium">Medium</option>
                <option value="High">High</option>
                <option value="Urgent">Urgent</option>
              </select>
            </div>

            <div class="form-group">
              <label for="status">Status</label>
              <select id="status" bind:value={eventFormData.status}>
                <option value="Scheduled">Scheduled</option>
                <option value="In Progress">In Progress</option>
                <option value="Completed">Completed</option>
                <option value="Cancelled">Cancelled</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label>Assigned Staff</label>
            <div class="staff-checkboxes">
              {#each $staff.filter((s: any) => s.isActive) as staffMember}
                <label class="checkbox-label">
                  <input
                    type="checkbox"
                    bind:group={eventFormData.assignedStaffIds}
                    value={staffMember.id}
                  />
                  {staffMember.fullName}
                </label>
              {/each}
            </div>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input
                type="checkbox"
                bind:checked={eventFormData.allDay}
              />
              All Day Event
            </label>
          </div>

          <div class="form-actions">
            <Button type="button" variant="tertiary" on:click={closeEventModal}>Cancel</Button>
            <Button type="submit">
              {editingEvent ? 'Update' : 'Create'} Event
            </Button>
          </div>
        </form>
      </div>
    </div>
  </div>
{/if}

<style lang="less">
  main {
    padding: 1rem 2rem;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .calendar-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;

    .navigation {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .calendar-title {
        margin: 0 0 0 1rem;
        font-size: 1.5rem;
        color: var(--black);
      }
    }

    .view-controls {
      display: flex;
      align-items: center;
      gap: 1rem;

      .view-buttons {
        display: flex;
        gap: 0.25rem;
      }

      .staff-filter {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        label {
          font-weight: 500;
          color: var(--black);
        }

        select {
          padding: 0.5rem;
          border: 1px solid var(--border);
          border-radius: var(--br);
          min-width: 150px;
        }
      }
    }
  }

  .calendar-container {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;
  }

  .month-view {
    .month-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: var(--bg);
      border-bottom: 1px solid var(--border);

      .day-header {
        padding: 1rem;
        text-align: center;
        font-weight: 600;
        color: var(--grey);
        border-right: 1px solid var(--border);

        &:last-child {
          border-right: none;
        }
      }
    }

    .month-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      grid-template-rows: repeat(6, 120px);

      .day-cell {
        border-right: 1px solid var(--border);
        border-bottom: 1px solid var(--border);
        padding: 0.5rem;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background: var(--bg);
        }

        &.today {
          background: var(--primary-fade);
        }

        &.other-month {
          opacity: 0.5;
        }

        &:last-child {
          border-right: none;
        }

        .day-number {
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        .day-events {
          .event-item {
            background: var(--primary);
            color: white;
            padding: 0.25rem;
            margin-bottom: 0.25rem;
            border-radius: 3px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: var(--primary-dark);
            }

            &.draggable {
              cursor: grab;

              &:active {
                cursor: grabbing;
              }
            }

            .event-title {
              display: block;
              font-weight: 500;
            }

            .event-time {
              font-size: 0.7rem;
              opacity: 0.9;
            }
          }
        }
      }
    }
  }

  .week-view {
    .week-header {
      display: grid;
      grid-template-columns: 80px repeat(7, 1fr);
      background: var(--bg);
      border-bottom: 1px solid var(--border);

      .time-column {
        // Empty space above time column
      }

      .day-header {
        padding: 1rem;
        text-align: center;
        border-right: 1px solid var(--border);

        &.today {
          background: var(--primary-fade);
          color: var(--primary);
        }

        &:last-child {
          border-right: none;
        }

        .day-name {
          font-weight: 600;
          font-size: 0.9rem;
          margin-bottom: 0.25rem;
        }

        .day-number {
          font-size: 1.2rem;
          font-weight: 700;
        }
      }
    }

    .week-grid {
      display: grid;
      grid-template-columns: 80px repeat(7, 1fr);
      max-height: 600px;
      overflow-y: auto;

      .time-column {
        background: var(--bg);
        border-right: 1px solid var(--border);

        .time-slot {
          height: 60px;
          padding: 0.5rem;
          border-bottom: 1px solid var(--border);
          font-size: 0.8rem;
          color: var(--grey);
          box-sizing: border-box;
          display: flex;
          align-items: flex-start;
          justify-content: center;
        }
      }

      .day-column {
        border-right: 1px solid var(--border);

        &:last-child {
          border-right: none;
        }

        .time-cell {
          height: 60px;
          border-bottom: 1px solid var(--border);
          position: relative;
          cursor: pointer;
          transition: background-color 0.2s;
          box-sizing: border-box;
          &:hover {
            background: var(--bg);
          }

          .week-event {
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            background: var(--primary);
            color: white;
            padding: 0.25rem;
            border-radius: 3px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 1;

            &:hover {
              background: var(--primary-dark);
            }

            &.draggable {
              cursor: grab;

              &:active {
                cursor: grabbing;
              }
            }

            .event-title {
              font-weight: 500;
              margin-bottom: 0.125rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .event-time {
              font-size: 0.65rem;
              opacity: 0.9;
            }
          }
        }
      }
    }
  }

  .day-view {
    padding: 2rem;

    h3 {
      margin: 0 0 2rem 0;
      color: var(--black);
    }

    .day-events {
      .event-card {
        background: white;
        border: 1px solid var(--border);
        border-radius: var(--br);
        padding: 1.5rem;
        margin-bottom: 1rem;
        cursor: pointer;
        transition: box-shadow 0.2s;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .event-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 1rem;

          h4 {
            margin: 0;
            color: var(--primary);
          }
        }

        .event-details {
          p {
            margin: 0.5rem 0;
            color: var(--grey);

            strong {
              color: var(--black);
            }
          }
        }
      }

      .no-events {
        text-align: center;
        padding: 4rem 2rem;

        p {
          margin: 0 0 2rem 0;
          color: var(--grey);
        }
      }
    }
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border);

    h2 {
      margin: 0;
    }

    .close-button {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: var(--grey);

      &:hover {
        color: var(--black);
      }
    }
  }

  .modal-body {
    padding: 20px;
  }

  .event-form {
    .form-group {
      margin-bottom: 1rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--black);
      }

      input, textarea, select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }

        &.error {
          border-color: var(--red);
        }
      }

      .error-message {
        color: var(--red);
        font-size: 0.8rem;
        margin-top: 0.25rem;
      }
    }

    .form-row {
      display: flex;
      gap: 1rem;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 0.5rem;
      }
    }

    .staff-checkboxes {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.5rem;
      margin-top: 0.5rem;
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: normal;

      input[type="checkbox"] {
        width: auto;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid var(--border);
    }
  }

  @media (max-width: 768px) {
    .calendar-controls {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;

      .navigation {
        justify-content: center;

        .calendar-title {
          margin-left: 0.5rem;
          font-size: 1.2rem;
        }
      }

      .view-controls {
        justify-content: space-between;
      }
    }

    .month-grid {
      grid-template-rows: repeat(6, 80px) !important;
    }
  }
</style>