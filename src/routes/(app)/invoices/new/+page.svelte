<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { goto, beforeNavigate } from '$app/navigation';
  import { browser } from '$app/environment';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import Button from '$lib/components/Button.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { contactStore, customers } from '$lib/stores/customerStore';
  import CustomerSelect from '$lib/components/CustomerSelect.svelte';
  import {
    createInvoice,
    getProducts,
    getInvoiceTemplates,
    getInvoiceStatuses,
    calculateInvoiceTotals,
    type Invoice,
    type InvoiceLineItem,
    type Product,
    type InvoiceTemplate,
    type InvoiceStatus,
    type CustomHeaderField
  } from '$lib/api/invoices';
  import { getUninvoicedJobs, type Job } from '$lib/api/jobs';
  import { getUninvoicedQuotes, type Quote } from '$lib/api/quotes';
  import type { Contact } from '$lib/api/contacts';

  // Define interfaces for our form data
  interface InvoiceFormData {
    invoiceNumber: string;
    customerId: string;
    issueDate: string;
    dueDate: string;
    status: InvoiceStatus;
    lineItems: InvoiceLineItem[];
    customHeaderFields: CustomHeaderField[];
    notes: string;
    terms: string;
    templateId: string;
    subtotal: number;
    taxAmount: number;
    discountAmount: number;
    totalAmount: number;
  }

  // State variables
  let isLoading = false;
  let isSaving = false;
  let customerContacts: Contact[] = [];
  let products: Product[] = [];
  let invoiceTemplates: InvoiceTemplate[] = [];
  let invoiceStatuses: InvoiceStatus[] = [];
  let uninvoicedJobs: Job[] = [];
  let uninvoicedQuotes: Quote[] = [];
  let isLoadingData = true;
  let isLoadingUninvoiced = false;
  let customerSearch = '';
  let showCustomerDropdown = false;
  let highlightedIndex = -1;
  let formChanged = false;
  let initialFormState: string;
  let showUninvoicedSection = false;

  // Initialize form data with default values
  let formData: InvoiceFormData = {
    invoiceNumber: '', // Will be generated
    customerId: '',
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    status: { id: '1', name: 'Draft', color: '#6B7280' },
    lineItems: [createEmptyLineItem()],
    customHeaderFields: [],
    notes: '',
    terms: 'Payment due within 30 days. Please make payment via bank transfer.',
    templateId: '',
    subtotal: 0,
    taxAmount: 0,
    discountAmount: 0,
    totalAmount: 0
  };

  // Form validation
  let errors: Record<string, string> = {};
  let formSubmitted = false;

  // Helper functions
  function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  function updateFormChanged() {
    if (!initialFormState) return;
    const currentFormState = JSON.stringify(formData);
    formChanged = currentFormState !== initialFormState;
  }

  function createEmptyLineItem(): InvoiceLineItem {
    return {
      id: generateId(),
      description: '',
      quantity: 1,
      unitPrice: 0,
      taxRate: 10, // Default tax rate (10%)
      taxAmount: 0,
      lineTotal: 0,
      additionalInfo: ''
    };
  }

  function createEmptyCustomField(): CustomHeaderField {
    return {
      id: generateId(),
      label: '',
      value: '',
      type: 'text'
    };
  }

  function calculateTotals() {
    // Calculate line totals and tax amounts
    const updatedLineItems = formData.lineItems.map(item => {
      const lineTotal = item.quantity * item.unitPrice;
      const taxAmount = lineTotal * (item.taxRate / 100);
      return {
        ...item,
        lineTotal,
        taxAmount
      };
    });

    const totals = calculateInvoiceTotals(updatedLineItems, formData.discountAmount);

    formData = {
      ...formData,
      lineItems: updatedLineItems,
      subtotal: totals.subtotal,
      taxAmount: totals.taxAmount,
      totalAmount: totals.totalAmount
    };

    updateFormChanged();
  }

  function addLineItem() {
    formData = {
      ...formData,
      lineItems: [...formData.lineItems, createEmptyLineItem()]
    };
    updateFormChanged();
  }

  function removeLineItem(index: number) {
    formData = {
      ...formData,
      lineItems: formData.lineItems.filter((_, i) => i !== index)
    };
    calculateTotals();
    updateFormChanged();
  }

  function addCustomHeaderField() {
    formData = {
      ...formData,
      customHeaderFields: [...formData.customHeaderFields, createEmptyCustomField()]
    };
    updateFormChanged();
  }

  function removeCustomHeaderField(index: number) {
    formData = {
      ...formData,
      customHeaderFields: formData.customHeaderFields.filter((_, i) => i !== index)
    };
    updateFormChanged();
  }

  function selectProduct(lineItemIndex: number, product: Product) {
    const updatedLineItems = [...formData.lineItems];
    updatedLineItems[lineItemIndex] = {
      ...updatedLineItems[lineItemIndex],
      productId: product.id,
      description: product.description,
      unitPrice: product.price,
      taxRate: product.taxRate
    };

    formData = {
      ...formData,
      lineItems: updatedLineItems
    };

    calculateTotals();
  }

  function validateForm(): boolean {
    errors = {};

    if (!formData.customerId) {
      errors.customerId = 'Please select a customer';
    }

    if (!formData.issueDate) {
      errors.issueDate = 'Issue date is required';
    }

    if (!formData.dueDate) {
      errors.dueDate = 'Due date is required';
    }

    // Validate each line item
    formData.lineItems.forEach((item, index) => {
      if (!item.description) {
        errors[`lineItems[${index}].description`] = 'Description is required';
      }

      if (item.quantity <= 0) {
        errors[`lineItems[${index}].quantity`] = 'Quantity must be greater than 0';
      }

      if (item.unitPrice < 0) {
        errors[`lineItems[${index}].unitPrice`] = 'Unit price cannot be negative';
      }
    });

    // Validate custom header fields
    formData.customHeaderFields.forEach((field, index) => {
      if (!field.label) {
        errors[`customHeaderFields[${index}].label`] = 'Field label is required';
      }
    });

    return Object.keys(errors).length === 0;
  }

  async function loadData() {
    isLoadingData = true;
    try {
      // Load contacts/customers
      await contactStore.loadContacts();

      // Load other data in parallel
      const [productsData, templatesData, statusesData] = await Promise.all([
        getProducts(),
        getInvoiceTemplates(),
        getInvoiceStatuses()
      ]);

      products = productsData;
      invoiceTemplates = templatesData;
      invoiceStatuses = statusesData;

      // Set default status
      if (invoiceStatuses.length > 0) {
        formData.status = invoiceStatuses.find(s => s.name === 'Draft') || invoiceStatuses[0];
      }

      // Set default template
      if (invoiceTemplates.length > 0) {
        const defaultTemplate = invoiceTemplates.find(t => t.isDefault);
        if (defaultTemplate) {
          formData.templateId = defaultTemplate.id;
        }
      }

    } catch (error) {
      console.error('Error loading data:', error);
      addToast({ message: 'Failed to load data', type: 'error' });
    } finally {
      isLoadingData = false;
    }
  }

  function filterCustomers(searchTerm: string): Contact[] {
    if (!searchTerm.trim()) {
      return $customers;
    }
    const term = searchTerm.toLowerCase();
    return $customers.filter(customer =>
      customer.fullName.toLowerCase().includes(term) ||
      (customer.companyName && customer.companyName.toLowerCase().includes(term)) ||
      customer.emails.some(email => email.email.toLowerCase().includes(term))
    );
  }

  function handleCustomerSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    customerSearch = target.value;
    showCustomerDropdown = true;
    highlightedIndex = -1;
    updateFormChanged();
  }

  function selectCustomer(customer: Contact) {
    formData = {
      ...formData,
      customerId: customer.id
    };
    customerSearch = customer.companyName || customer.fullName;
    showCustomerDropdown = false;
    updateFormChanged();

    // Load uninvoiced items for this customer
    loadUninvoicedItems(customer.id);
  }

  async function loadUninvoicedItems(customerId: string) {
    if (!customerId) {
      uninvoicedJobs = [];
      uninvoicedQuotes = [];
      return;
    }

    isLoadingUninvoiced = true;
    try {
      const [jobs, quotes] = await Promise.all([
        getUninvoicedJobs(customerId),
        getUninvoicedQuotes(customerId)
      ]);

      uninvoicedJobs = jobs;
      uninvoicedQuotes = quotes;

      // Show the section if there are uninvoiced items
      showUninvoicedSection = jobs.length > 0 || quotes.length > 0;
    } catch (error) {
      console.error('Error loading uninvoiced items:', error);
      addToast({ message: 'Failed to load uninvoiced items', type: 'error' });
    } finally {
      isLoadingUninvoiced = false;
    }
  }

  function addJobToInvoice(job: Job) {
    const newLineItem: InvoiceLineItem = {
      id: generateId(),
      description: `${job.title} - ${job.description}`,
      quantity: 1,
      unitPrice: 0, // No estimated cost in Job interface
      taxRate: 10,
      taxAmount: 0,
      lineTotal: 0,
      additionalInfo: `Job ID: ${job.id}`,
      relatedJobId: job.id
    };

    formData = {
      ...formData,
      lineItems: [...formData.lineItems, newLineItem]
    };

    calculateTotals();
    addToast({ message: 'Job added to invoice', type: 'success' });
  }

  function addQuoteToInvoice(quote: Quote) {
    // Add each line item from the quote
    const newLineItems = quote.lineItems.map(item => ({
      id: generateId(),
      description: item.description,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      taxRate: item.taxRate,
      taxAmount: 0,
      lineTotal: 0,
      additionalInfo: item.additionalInfo || `Quote ID: ${quote.id}`,
      quoteId: quote.id
    }));

    formData = {
      ...formData,
      lineItems: [...formData.lineItems, ...newLineItems]
    };

    calculateTotals();
    addToast({ message: 'Quote items added to invoice', type: 'success' });
  }

  function addMultipleItems(selectedJobs: string[], selectedQuotes: string[]) {
    // Add selected jobs
    selectedJobs.forEach(jobId => {
      const job = uninvoicedJobs.find(j => j.id === jobId);
      if (job) addJobToInvoice(job);
    });

    // Add selected quotes
    selectedQuotes.forEach(quoteId => {
      const quote = uninvoicedQuotes.find(q => q.id === quoteId);
      if (quote) addQuoteToInvoice(quote);
    });
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (!showCustomerDropdown) return;

    const filteredCustomers = filterCustomers(customerSearch);

    if (event.key === 'ArrowDown') {
      event.preventDefault();
      highlightedIndex = Math.min(highlightedIndex + 1, filteredCustomers.length - 1);
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      highlightedIndex = Math.max(highlightedIndex - 1, -1);
    } else if (event.key === 'Enter' && highlightedIndex >= 0) {
      event.preventDefault();
      selectCustomer(filteredCustomers[highlightedIndex]);
    } else if (event.key === 'Escape') {
      showCustomerDropdown = false;
    }
  }

  function handleInputBlur() {
    // Small delay to allow click events to fire on dropdown items
    setTimeout(() => {
      showCustomerDropdown = false;
    }, 200);
  }

  async function handleSubmit() {
    formSubmitted = true;
    calculateTotals();

    if (!validateForm()) {
      addToast({
        message: 'Please fix the errors in the form before submitting',
        type: 'error'
      });
      return;
    }

    isSaving = true;

    try {
      // Get selected customer details
      const selectedCustomer = $customers.find(c => c.id === formData.customerId);
      const primaryAddress = selectedCustomer?.addresses.find(addr => addr.isPrimary);

      // Prepare the invoice data
      const invoiceData: Omit<Invoice, 'id' | 'invoiceNumber' | 'createdAt' | 'updatedAt'> = {
        customerId: formData.customerId,
        customerName: selectedCustomer?.companyName || selectedCustomer?.fullName,
        customerEmail: selectedCustomer?.emails.find(e => e.isPrimary)?.email,
        customerAddress: primaryAddress ? {
          street: primaryAddress.street,
          city: primaryAddress.city,
          state: primaryAddress.state,
          zipCode: primaryAddress.zipCode,
          country: primaryAddress.country
        } : undefined,
        issueDate: formData.issueDate,
        dueDate: formData.dueDate,
        status: formData.status,
        lineItems: formData.lineItems,
        customHeaderFields: formData.customHeaderFields,
        subtotal: formData.subtotal,
        taxAmount: formData.taxAmount,
        discountAmount: formData.discountAmount,
        totalAmount: formData.totalAmount,
        paidAmount: 0,
        balanceAmount: formData.totalAmount,
        notes: formData.notes,
        terms: formData.terms,
        templateId: formData.templateId || undefined
      };

      const newInvoice = await createInvoice(invoiceData);

      addToast({
        message: 'Invoice created successfully',
        type: 'success'
      });

      // Navigate back to invoices list
      goto('/invoices');
    } catch (err) {
      console.error('Error creating invoice:', err);
      addToast({
        message: err instanceof Error ? err.message : 'An unknown error occurred',
        type: 'error'
      });
    } finally {
      isSaving = false;
    }
  }

  function cancelForm() {
    updateFormChanged();
    if (formChanged) {
      if (window.confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        goto('/invoices');
      }
    } else {
      goto('/invoices');
    }
  }

  // Watch for changes to recalculate totals and update form state
  $: if (formData.lineItems) {
    calculateTotals();
  }

  // React to form data changes
  $: if (initialFormState) {
    updateFormChanged();
  }

  // Handle navigation confirmation
  beforeNavigate(({ cancel, to }) => {
    // Skip if we're saving or if the form hasn't changed
    if (isSaving) return;

    updateFormChanged();

    if (formChanged && to?.url.pathname !== window.location.pathname) {
      if (!window.confirm('You have unsaved changes. Are you sure you want to leave?')) {
        cancel();
      }
    }
  });

  onMount(async () => {
    await loadData();
    // Set initial form state after data is loaded
    setTimeout(() => {
      initialFormState = JSON.stringify(formData);
    }, 100);
  });

  // Reactive statement for filtered customers
  $: filteredCustomers = filterCustomers(customerSearch);
</script>

<svelte:head>
  <title>Create Invoice</title>
</svelte:head>

<div class="container">
  <PageHeader title="Create Invoice">
    <svelte:fragment slot="actions">
      <div class="save-status-container">
        {#if formChanged}
          <div class="save-status unsaved">
            <div class="status-dot"></div>
            Unsaved changes
          </div>
        {:else}
          <div class="save-status saved">
            <div class="status-dot"></div>
            All changes saved
          </div>
        {/if}
      </div>
    </svelte:fragment>
  </PageHeader>

  <main>
  {#if isLoadingData}
    <div class="loading-container">
      <LoadingSpinner />
      <p>Loading invoice data...</p>
    </div>
  {:else}
    <form on:submit|preventDefault={handleSubmit} class="invoice-form">
      <!-- Invoice Header -->
      <div class="form-section">
        <h2>Invoice Details</h2>

        <div class="form-row">
          <div class="form-group">
            <label for="invoiceNumber">Invoice Number</label>
            <input
              type="text"
              id="invoiceNumber"
              bind:value={formData.invoiceNumber}
              placeholder="Auto-generated"
              readonly
            />
          </div>

          <div class="form-group">
            <label for="status">Status</label>
            <select id="status" bind:value={formData.status}>
              {#each invoiceStatuses as status}
                <option value={status}>{status.name}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="template">Template</label>
            <select id="template" bind:value={formData.templateId}>
              <option value="">Default Template</option>
              {#each invoiceTemplates as template}
                <option value={template.id}>{template.name}</option>
              {/each}
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="issueDate">Issue Date</label>
            <input
              type="date"
              id="issueDate"
              bind:value={formData.issueDate}
              class:error={formSubmitted && errors.issueDate}
            />
            {#if formSubmitted && errors.issueDate}
              <div class="error-message">{errors.issueDate}</div>
            {/if}
          </div>

          <div class="form-group">
            <label for="dueDate">Due Date</label>
            <input
              type="date"
              id="dueDate"
              bind:value={formData.dueDate}
              class:error={formSubmitted && errors.dueDate}
            />
            {#if formSubmitted && errors.dueDate}
              <div class="error-message">{errors.dueDate}</div>
            {/if}
          </div>
        </div>
      </div>

      <!-- Customer Information -->
      <div class="form-section">
        <h2>Customer Information</h2>

        <CustomerSelect
          bind:customerId={formData.customerId}
          bind:customerSearch={customerSearch}
          hasError={formSubmitted && !!errors.customerId}
          errorMessage={formSubmitted && errors.customerId ? errors.customerId : ''}
          on:selectcustomer={(event) => loadUninvoicedItems(event.detail)}
        />
      </div>

      <!-- Uninvoiced Items -->
      {#if showUninvoicedSection && formData.customerId}
        <div class="form-section">
          <h2>Uninvoiced Items</h2>
          <p class="section-description">Add existing jobs and quotes to this invoice</p>

          {#if isLoadingUninvoiced}
            <div class="loading-uninvoiced">
              <LoadingSpinner />
              <span>Loading uninvoiced items...</span>
            </div>
          {:else}
            <div class="uninvoiced-container">
              <!-- Uninvoiced Jobs -->
              {#if uninvoicedJobs.length > 0}
                <div class="uninvoiced-section">
                  <h3>Uninvoiced Jobs ({uninvoicedJobs.length})</h3>
                  <div class="uninvoiced-items">
                    {#each uninvoicedJobs as job}
                      <div class="uninvoiced-item">
                        <div class="item-info">
                          <div class="item-title">{job.title}</div>
                          <div class="item-details">
                            <span class="item-type">{job.jobType || 'Job'}</span>
                            <span class="item-date">Scheduled: {job.scheduledDateTime ? new Date(job.scheduledDateTime).toLocaleDateString() : 'Not scheduled'}</span>
                            <span class="item-cost">No cost estimate</span>
                          </div>
                          {#if job.description}
                            <div class="item-description">{job.description}</div>
                          {/if}
                        </div>
                        <div class="item-actions">
                          <Button variant="secondary" size="small" on:click={() => addJobToInvoice(job)}>
                            Add to Invoice
                          </Button>
                        </div>
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}

              <!-- Uninvoiced Quotes -->
              {#if uninvoicedQuotes.length > 0}
                <div class="uninvoiced-section">
                  <h3>Uninvoiced Quotes ({uninvoicedQuotes.length})</h3>
                  <div class="uninvoiced-items">
                    {#each uninvoicedQuotes as quote}
                      <div class="uninvoiced-item">
                        <div class="item-info">
                          <div class="item-title">Quote #{quote.quoteNumber}</div>
                          <div class="item-details">
                            <span class="item-date">Issued: {new Date(quote.issueDate).toLocaleDateString()}</span>
                            <span class="item-cost">${quote.totalAmount.toFixed(2)}</span>
                            <span class="item-status" class:accepted={quote.status.name === 'Accepted'}>
                              {quote.status.name}
                            </span>
                          </div>
                          <div class="item-description">{quote.lineItems.length} line item{quote.lineItems.length !== 1 ? 's' : ''}</div>
                        </div>
                        <div class="item-actions">
                          <Button variant="secondary" size="small" on:click={() => addQuoteToInvoice(quote)}>
                            Add to Invoice
                          </Button>
                        </div>
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}

              {#if uninvoicedJobs.length === 0 && uninvoicedQuotes.length === 0}
                <div class="no-uninvoiced">
                  <p>No uninvoiced jobs or quotes found for this customer.</p>
                </div>
              {/if}
            </div>
          {/if}
        </div>
      {/if}

      <!-- Custom Header Fields -->
      {#if formData.customHeaderFields.length > 0}
        <div class="form-section">
          <h2>Custom Header Fields</h2>

          {#each formData.customHeaderFields as field, index}
            <div class="custom-field-row">
              <div class="form-group">
                <label for="fieldLabel{index}">Field Label</label>
                <input
                  type="text"
                  id="fieldLabel{index}"
                  bind:value={field.label}
                  placeholder="Field label"
                  class:error={formSubmitted && errors[`customHeaderFields[${index}].label`]}
                />
                {#if formSubmitted && errors[`customHeaderFields[${index}].label`]}
                  <div class="error-message">{errors[`customHeaderFields[${index}].label`]}</div>
                {/if}
              </div>

              <div class="form-group">
                <label for="fieldType{index}">Type</label>
                <select id="fieldType{index}" bind:value={field.type}>
                  <option value="text">Text</option>
                  <option value="date">Date</option>
                  <option value="number">Number</option>
                </select>
              </div>

              <div class="form-group">
                <label for="fieldValue{index}">Value</label>
                {#if field.type === 'date'}
                  <input
                    type="date"
                    id="fieldValue{index}"
                    bind:value={field.value}
                  />
                {:else if field.type === 'number'}
                  <input
                    type="number"
                    id="fieldValue{index}"
                    bind:value={field.value}
                    step="0.01"
                  />
                {:else}
                  <input
                    type="text"
                    id="fieldValue{index}"
                    bind:value={field.value}
                    placeholder="Field value"
                  />
                {/if}
              </div>

              <div class="form-group">
                <Button
                  variant="tertiary"
                  size="small"
                  on:click={() => removeCustomHeaderField(index)}
                >
                  Remove
                </Button>
              </div>
            </div>
          {/each}
        </div>
      {/if}

      <div class="form-section">
        <div class="section-header">
          <h2>Custom Header Fields</h2>
          <Button variant="secondary" size="small" on:click={addCustomHeaderField}>
            Add Custom Field
          </Button>
        </div>
      </div>

      <!-- Invoice Items -->
      <div class="form-section">
        <h2>Invoice Items</h2>

        <div class="items-table">
          <div class="items-header">
            <div class="item-cell">Description</div>
            <div class="item-cell">Quantity</div>
            <div class="item-cell">Unit Price</div>
            <div class="item-cell">Tax Rate (%)</div>
            <div class="item-cell">Line Total</div>
            <div class="item-cell actions"></div>
          </div>

          {#each formData.lineItems as item, index}
            <div class="items-row">
              <div class="item-cell">
                <div class="item-description">
                                     <select
                     on:change={(e) => {
                       const target = e.target as HTMLSelectElement;
                       const productId = target.value;
                       if (productId) {
                         const product = products.find(p => p.id === productId);
                         if (product) selectProduct(index, product);
                       }
                     }}
                     class="product-select"
                   >
                    <option value="">Select product or enter manually</option>
                    {#each products as product}
                      <option value={product.id}>{product.name}</option>
                    {/each}
                  </select>

                  <input
                    type="text"
                    bind:value={item.description}
                    placeholder="Item description"
                    class:error={formSubmitted && errors[`lineItems[${index}].description`]}
                  />

                  <textarea
                    bind:value={item.additionalInfo}
                    placeholder="Additional info (optional)"
                    rows="2"
                    class="additional-info"
                  ></textarea>
                </div>

                {#if formSubmitted && errors[`lineItems[${index}].description`]}
                  <div class="error-message">{errors[`lineItems[${index}].description`]}</div>
                {/if}
              </div>

              <div class="item-cell">
                <input
                  type="number"
                  bind:value={item.quantity}
                  min="1"
                  step="1"
                  on:input={() => calculateTotals()}
                  class:error={formSubmitted && errors[`lineItems[${index}].quantity`]}
                />
                {#if formSubmitted && errors[`lineItems[${index}].quantity`]}
                  <div class="error-message">{errors[`lineItems[${index}].quantity`]}</div>
                {/if}
              </div>

              <div class="item-cell">
                <input
                  type="number"
                  bind:value={item.unitPrice}
                  min="0"
                  step="0.01"
                  on:input={() => calculateTotals()}
                  class:error={formSubmitted && errors[`lineItems[${index}].unitPrice`]}
                />
                {#if formSubmitted && errors[`lineItems[${index}].unitPrice`]}
                  <div class="error-message">{errors[`lineItems[${index}].unitPrice`]}</div>
                {/if}
              </div>

              <div class="item-cell">
                <input
                  type="number"
                  bind:value={item.taxRate}
                  min="0"
                  max="100"
                  step="0.1"
                  on:input={() => calculateTotals()}
                />
              </div>

              <div class="item-cell amount">
                ${item.lineTotal.toFixed(2)}
              </div>

              <div class="item-cell actions">
                {#if formData.lineItems.length > 1}
                  <Button
                    variant="tertiary"
                    size="small"
                    on:click={() => removeLineItem(index)}
                  >
                    Remove
                  </Button>
                {/if}
              </div>
            </div>
          {/each}

          <div class="add-item-row">
            <Button variant="secondary" size="small" on:click={addLineItem}>
              Add Item
            </Button>
          </div>
        </div>

        <div class="invoice-totals">
          <div class="totals-row">
            <div class="totals-label">Subtotal:</div>
            <div class="totals-value">${formData.subtotal.toFixed(2)}</div>
          </div>

          <div class="totals-row">
            <div class="totals-label">
              <label for="discountAmount">Discount:</label>
            </div>
            <div class="totals-value">
              <input
                type="number"
                id="discountAmount"
                bind:value={formData.discountAmount}
                min="0"
                step="0.01"
                on:input={() => calculateTotals()}
                class="discount-input"
              />
            </div>
          </div>

          <div class="totals-row">
            <div class="totals-label">Tax:</div>
            <div class="totals-value">${formData.taxAmount.toFixed(2)}</div>
          </div>

          <div class="totals-row total">
            <div class="totals-label">Total:</div>
            <div class="totals-value">${formData.totalAmount.toFixed(2)}</div>
          </div>
        </div>
      </div>

      <!-- Additional Information -->
      <div class="form-section">
        <h2>Additional Information</h2>

        <div class="form-group">
          <label for="notes">Notes</label>
          <textarea id="notes" bind:value={formData.notes} rows="3" placeholder="Notes to the customer"></textarea>
        </div>

        <div class="form-group">
          <label for="terms">Terms and Conditions</label>
          <textarea id="terms" bind:value={formData.terms} rows="3"></textarea>
        </div>
      </div>

      <div class="form-actions">
        <Button variant="tertiary" on:click={cancelForm} disabled={isSaving}>Cancel</Button>
        <Button type="submit" disabled={isSaving}>
          {#if isSaving}
            Saving...
          {:else}
            Save Invoice
          {/if}
        </Button>
      </div>
    </form>
  {/if}
  </main>

</div>

<style lang="less">

  .save-status-container {
    display: flex;
    align-items: center;
    margin-right: 15px;
  }

  .save-status {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;

    &.saved {
      color: var(--green);
      background-color: var(--green-fade);
    }

    &.unsaved {
      color: var(--orange, #f59e0b);
      background-color: var(--orange-fade, rgba(245, 158, 11, 0.1));
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }

    &.saved .status-dot {
      background-color: var(--green);
    }

    &.unsaved .status-dot {
      background-color: var(--orange, #f59e0b);
    }
  }

  .invoice-form {
    max-width: 1200px;
    margin: 0 auto;
  }

  .form-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;

    .form-group {
      margin-bottom: 20px;
      position: relative;


    }

    h2 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 18px;
      color: var(--primary);
      border-bottom: 1px solid var(--border);
      padding-bottom: 10px;
    }
  }

  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 10px;
    }
  }

  .items-table {
    border: 1px solid var(--border);
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .items-header {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr 1fr 80px;
    background-color: var(--bg);
    gap: 10px;
    border-bottom: 1px solid var(--border);
    font-weight: 500;
    color: var(--grey);

    .item-cell {
      padding: 10px;
      font-size: 14px;
    }
  }

  .items-row {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr 1fr 80px;
    border-bottom: 1px solid var(--border);
    align-items: center;
    font-size: 14px;
    gap: 10px;

    .item-cell {
      padding: 10px;

      &.amount {
        font-weight: 500;
      }

      input {
        width: 100%;
        padding: 8px;
        border: 1px solid var(--border);
        border-radius: 4px;

        &:focus {
          outline: none;
          border-color: var(--primary);
        }

        &.error {
          border-color: var(--red);
        }
      }
    }
  }

  .add-item-row {
    padding: 15px;
    text-align: left;
  }

  .invoice-totals {
    margin-left: auto;
    width: 300px;
    border-top: 1px solid var(--border);
    padding-top: 15px;
  }

  .totals-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    &.total {
      font-weight: 700;
      font-size: 18px;
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid var(--border);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h2 {
      margin: 0;
    }
  }

  .custom-field-row {
    display: grid;
    grid-template-columns: 2fr 1fr 2fr auto;
    gap: 1rem;
    align-items: end;
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid var(--border);
    border-radius: var(--br);
    background: var(--bg);
  }

  .item-description {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .product-select {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border);
      border-radius: 4px;
      font-size: 0.9rem;
      background: white;

      &:focus {
        outline: none;
        border-color: var(--primary);
      }
    }

    .additional-info {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border);
      border-radius: 4px;
      font-size: 0.9rem;
      resize: vertical;
      font-family: inherit;

      &:focus {
        outline: none;
        border-color: var(--primary);
      }
    }
  }

  // Uninvoiced Items Styles
  .uninvoiced-container {
    border: 1px solid var(--border);
    border-radius: var(--br);
    background: var(--bg);
    padding: 1rem;
  }

  .uninvoiced-section {
    margin-bottom: 1.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    h3 {
      margin: 0 0 1rem 0;
      font-size: 16px;
      color: var(--primary);
      font-weight: 600;
    }
  }

  .uninvoiced-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .uninvoiced-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border);
    border-radius: var(--br);
    background: white;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--primary);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .item-info {
    flex: 1;

    .item-title {
      font-weight: 600;
      color: var(--text);
      margin-bottom: 0.25rem;
    }

    .item-details {
      display: flex;
      gap: 1rem;
      margin-bottom: 0.25rem;
      font-size: 0.9rem;

      .item-type {
        color: var(--primary);
        font-weight: 500;
      }

      .item-date {
        color: var(--grey);
      }

      .item-cost {
        color: var(--green);
        font-weight: 600;
      }

      .item-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
        background: var(--grey-light);
        color: var(--grey);

        &.accepted {
          background: var(--green-light);
          color: var(--green);
        }
      }
    }

    .item-description {
      color: var(--grey);
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }

  .item-actions {
    flex-shrink: 0;
  }

  .loading-uninvoiced {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 2rem;
    justify-content: center;
    color: var(--grey);
  }

  .no-uninvoiced {
    text-align: center;
    padding: 2rem;
    color: var(--grey);
    font-style: italic;
  }

  .section-description {
    color: var(--grey);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    margin-top: -0.5rem;
  }

  .discount-input {
    width: 100px;
    padding: 8px;
    border: 1px solid var(--border);
    border-radius: 4px;
    text-align: right;

    &:focus {
      outline: none;
      border-color: var(--primary);
    }
  }

  @media (max-width: 768px) {
    .custom-field-row {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .items-header,
    .items-row {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .item-cell {
      padding: 0.5rem;
    }
  }
</style>
