<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { getInvoiceById, type Invoice } from '$lib/api/invoices';
  import { formatCurrency } from '$lib/config/currency';

  let invoiceId: string | null = null;
  let invoiceData: Invoice | null = null;
  let isLoading = true;
  let error: string | null = null;

  onMount(async () => {
    invoiceId = $page.params.invoiceId;
    if (invoiceId) {
      await fetchInvoiceDetails(invoiceId);
    } else {
      error = 'Invoice ID not provided.';
      isLoading = false;
    }
  });

  async function fetchInvoiceDetails(id: string) {
    isLoading = true;
    error = null;
    try {
      invoiceData = await getInvoiceById(id);
      if (!invoiceData) {
        throw new Error('Invoice not found');
      }
    } catch (err) {
      console.error('Error fetching invoice details:', err);
      error = err instanceof Error ? err.message : 'An unknown error occurred';
      addToast({
        type: 'error',
        message: `Failed to load invoice: ${error}`
      });
    } finally {
      isLoading = false;
    }
  }


</script>

<div class="container">
  <PageHeader title="Invoice Details">
    <!-- Actions can go here, e.g., Edit, Print -->
  </PageHeader>

  <main>
    {#if isLoading}
      <LoadingSpinner message="Loading invoice details..." />
    {:else if error}
      <div class="error-message">
        <p>Error: {error}</p>
      </div>
    {:else if invoiceData}
      <div class="invoice-details">
        <h2>Invoice Number: {invoiceData.invoiceNumber || 'N/A'}</h2>
        <p><strong>Customer Name:</strong> {invoiceData.customerName || 'N/A'}</p>
        <p><strong>Issue Date:</strong> {invoiceData.issueDate ? new Date(invoiceData.issueDate).toLocaleDateString() : 'N/A'}</p>
        <p><strong>Due Date:</strong> {invoiceData.dueDate ? new Date(invoiceData.dueDate).toLocaleDateString() : 'N/A'}</p>
        <p><strong>Status:</strong> {invoiceData.status?.name || 'N/A'}</p>
        <p><strong>Notes:</strong> {invoiceData.notes || 'N/A'}</p>
        <p><strong>Payment Terms:</strong> {invoiceData.terms || 'N/A'}</p>

        <h3>Line Items</h3>
        {#if invoiceData.lineItems && invoiceData.lineItems.length > 0}
          <table>
            <thead>
              <tr>
                <th>Description</th>
                <th>Quantity</th>
                <th>Unit Price</th>
                <th>Tax Rate</th>
                <th>Line Total</th>
              </tr>
            </thead>
            <tbody>
              {#each invoiceData.lineItems as item}
                <tr>
                  <td>{item.description}</td>
                  <td>{item.quantity}</td>
                  <td>{formatCurrency(item.unitPrice)}</td>
                  <td>{item.taxRate}%</td>
                  <td>{formatCurrency(item.lineTotal)}</td>
                </tr>
              {/each}
            </tbody>
          </table>
        {:else}
          <p>No line items found for this invoice.</p>
        {/if}

        <div class="totals">
          <p><strong>Subtotal:</strong> {formatCurrency(invoiceData.subtotal)}</p>
          <p><strong>Tax Total:</strong> {formatCurrency(invoiceData.taxAmount)}</p>
          <h3>Total: {formatCurrency(invoiceData.totalAmount)}</h3>
        </div>
      </div>
    {:else}
      <p>No invoice data available.</p>
    {/if}
  </main>
</div>

<style lang="less">
  .invoice-details {
    background-color: var(--bg-light);
    padding: 20px;
    border-radius: var(--br);
    box-shadow: var(--shadow);
  }

  .invoice-details h2, .invoice-details h3 {
    color: var(--primary);
    margin-bottom: 15px;
  }

  .invoice-details p {
    margin-bottom: 10px;
  }

  .invoice-details table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .invoice-details th, .invoice-details td {
    border: 1px solid var(--border);
    padding: 8px;
    text-align: left;
  }

  .invoice-details th {
    background-color: var(--bg-dark);
    color: var(--text-light);
  }

  .totals {
    text-align: right;
    margin-top: 20px;
    border-top: 1px solid var(--border);
    padding-top: 15px;
  }

  .totals p {
    margin-bottom: 5px;
  }

  .totals h3 {
    margin-top: 10px;
    color: var(--accent);
  }

  .error-message {
    padding: 20px;
    background-color: var(--red-fade);
    color: var(--red);
    border-radius: var(--br);
    margin: 20px 0;
    text-align: center;
  }
</style>