<script lang="ts">
  import { onMount } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import Tabs from '$lib/components/Tabs.svelte';
  import TemplateDesigner from '$lib/components/TemplateDesigner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { goto } from '$app/navigation';
  import {
    getInvoices,
    getInvoiceStatuses
  } from '$lib/api/invoices';
  import type { Invoice, InvoiceStatus } from '$lib/api/invoices';
  import { formatCurrency } from '$lib/config/currency';

  // Tab state
  let activeTab: 'invoices' | 'designer' = 'invoices';

  // Tab configuration
  const tabs = [
    { id: 'invoices', label: 'Invoices' },
    { id: 'designer', label: 'Template Designer' }
  ];

  function handleTabChange(event: CustomEvent<{ tabId: string }>) {
    activeTab = event.detail.tabId as 'invoices' | 'designer';
  }

  // Invoice list variables
  let invoices: Invoice[] = [];
  let invoiceStatuses: InvoiceStatus[] = [];
  let loading = true;
  let searchQuery = '';
  let statusFilter = 'All';

  // Template designer variables
  let isTemplateLoading = false;

  onMount(async () => {
    await loadData();
  });

  async function loadData() {
    loading = true;
    try {
      const [invoicesData, statusesData] = await Promise.all([
        getInvoices(),
        getInvoiceStatuses()
      ]);
      invoices = invoicesData;
      invoiceStatuses = statusesData;
    } catch (error) {
      console.error('Error loading invoices:', error);
      addToast({ message: 'Failed to load invoices', type: 'error' });
    } finally {
      loading = false;
    }
  }



  // Filtered invoices based on search and status
  $: filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         invoice.customerName?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'All' || invoice.status.name === statusFilter;

    return matchesSearch && matchesStatus;
  });

  function handleCreateInvoice() {
    goto('/invoices/new');
  }

  function handleViewInvoice(invoice: Invoice) {
    goto(`/invoices/${invoice.id}`);
  }

  function getStatusColor(status: InvoiceStatus): string {
    return status.color;
  }



  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  function getOverdueInvoices(): Invoice[] {
    const today = new Date();
    return invoices.filter(invoice =>
      invoice.status.name !== 'Paid' &&
      invoice.status.name !== 'Cancelled' &&
      new Date(invoice.dueDate) < today
    );
  }

  function getDraftInvoices(): Invoice[] {
    return invoices.filter(invoice => invoice.status.name === 'Draft');
  }

  function getPaidInvoices(): Invoice[] {
    return invoices.filter(invoice => invoice.status.name === 'Paid');
  }

  function getTotalRevenue(): number {
    return invoices
      .filter(invoice => invoice.status.name === 'Paid')
      .reduce((total, invoice) => total + invoice.totalAmount, 0);
  }


</script>

<svelte:head>
  <title>Invoices</title>
</svelte:head>

<div class="container">
  <PageHeader title="Invoices">
    <svelte:fragment slot="actions">
      <Button on:click={handleCreateInvoice} variant="primary" type="button">
        Create Invoice
      </Button>
    </svelte:fragment>
  </PageHeader>

  <!-- Tab Navigation -->
  <Tabs {tabs} {activeTab} on:change={handleTabChange} />

  <main>
    {#if activeTab === 'invoices'}
      {#if loading}
        <div class="loading-container">
          <LoadingSpinner />
          <p>Loading invoices...</p>
        </div>
      {:else}
      <!-- Stats -->
      <div class="stats">
        <div class="stat-card">
          <h3>Total Revenue</h3>
          <p class="stat-number">{formatCurrency(getTotalRevenue())}</p>
        </div>
        <div class="stat-card">
          <h3>Total Invoices</h3>
          <p class="stat-number">{invoices.length}</p>
        </div>
        <div class="stat-card">
          <h3>Paid</h3>
          <p class="stat-number">{getPaidInvoices().length}</p>
        </div>
        <div class="stat-card">
          <h3>Overdue</h3>
          <p class="stat-number overdue">{getOverdueInvoices().length}</p>
        </div>
        <div class="stat-card">
          <h3>Drafts</h3>
          <p class="stat-number">{getDraftInvoices().length}</p>
        </div>
      </div>

      <!-- Filters and Search -->
      <div class="controls">
        <div class="search-section">
          <input
            type="text"
            placeholder="Search invoices..."
            bind:value={searchQuery}
            class="search-input"
          />
        </div>

        <div class="filter-section">
          <label for="status-filter">Status:</label>
          <select id="status-filter" bind:value={statusFilter}>
            <option value="All">All</option>
            {#each invoiceStatuses as status}
              <option value={status.name}>{status.name}</option>
            {/each}
          </select>
        </div>
      </div>

      <!-- Invoices List -->
      {#if filteredInvoices.length === 0}
        <div class="empty-state">
          <h3>No invoices found</h3>
          <p>
            {#if searchQuery || statusFilter !== 'All'}
              Try adjusting your search or filters.
            {:else}
              Get started by creating your first invoice.
            {/if}
          </p>
          {#if !searchQuery && statusFilter === 'All'}
            <Button on:click={handleCreateInvoice} variant="primary">
              Create Invoice
            </Button>
          {/if}
        </div>
      {:else}
        <div class="invoices-table">
          <div class="table-header">
            <div class="header-cell">Invoice #</div>
            <div class="header-cell">Customer</div>
            <div class="header-cell">Issue Date</div>
            <div class="header-cell">Due Date</div>
            <div class="header-cell">Amount</div>
            <div class="header-cell">Status</div>
            <div class="header-cell">Actions</div>
          </div>

          {#each filteredInvoices as invoice (invoice.id)}
            <div class="table-row" on:click={() => handleViewInvoice(invoice)} role="button" tabindex="0">
              <div class="table-cell">
                <span class="invoice-number">{invoice.invoiceNumber}</span>
              </div>
              <div class="table-cell">
                <span class="customer-name">{invoice.customerName || 'Unknown Customer'}</span>
              </div>
              <div class="table-cell">
                <span class="date">{formatDate(invoice.issueDate)}</span>
              </div>
              <div class="table-cell">
                <span class="date" class:overdue={new Date(invoice.dueDate) < new Date() && invoice.status.name !== 'Paid'}>
                  {formatDate(invoice.dueDate)}
                </span>
              </div>
              <div class="table-cell">
                <span class="amount">{formatCurrency(invoice.totalAmount)}</span>
              </div>
              <div class="table-cell">
                <span class="status-badge" style="background-color: {getStatusColor(invoice.status)}">
                  {invoice.status.name}
                </span>
              </div>
              <div class="table-cell">
                <Button
                  variant="secondary"
                  size="small"
                  on:click={(e) => { e.stopPropagation(); handleViewInvoice(invoice); }}
                >
                  View
                </Button>
              </div>
            </div>
          {/each}
        </div>
      {/if}
      {/if}
    {:else if activeTab === 'designer'}
      <!-- Template Designer Component -->
      <TemplateDesigner isLoading={isTemplateLoading} />
    {/if}
  </main>
</div>

<style lang="less">


  main {
    padding: 1rem 2rem;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .stat-card {
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 1.5rem;
      text-align: center;

      h3 {
        margin: 0 0 0.5rem 0;
        font-size: 0.9rem;
        color: var(--grey);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-number {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
        color: var(--primary);

        &.overdue {
          color: #EF4444;
        }
      }
    }
  }

  .controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;

    .search-section {
      flex: 1;
      max-width: 400px;

      .search-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      label {
        font-weight: 500;
        color: var(--black);
      }

      select {
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        min-width: 120px;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    p {
      margin: 0 0 2rem 0;
      color: var(--grey);
    }
  }

  .invoices-table {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;

    .table-header {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr auto;
      background: var(--bg);
      border-bottom: 1px solid var(--border);

      .header-cell {
        padding: 1rem;
        font-weight: 600;
        color: var(--black);
        font-size: 0.9rem;
      }
    }

    .table-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr auto;
      border-bottom: 1px solid var(--border);
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: var(--bg);
      }

      &:last-child {
        border-bottom: none;
      }

      .table-cell {
        padding: 1rem;
        display: flex;
        align-items: center;
        font-size: 0.9rem;

        .invoice-number {
          font-weight: 600;
          color: var(--primary);
        }

        .customer-name {
          color: var(--black);
        }

        .date {
          color: var(--grey);

          &.overdue {
            color: #EF4444;
            font-weight: 500;
          }
        }

        .amount {
          font-weight: 600;
          color: var(--black);
        }

        .status-badge {
          font-size: 0.7rem;
          font-weight: 500;
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: var(--br);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .controls {
      flex-direction: column;
      align-items: stretch;
    }

    .invoices-table {
      .table-header,
      .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .header-cell,
      .table-cell {
        padding: 0.5rem 1rem;
      }

      .table-row {
        padding: 1rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        margin-bottom: 1rem;
        background: white;

        .table-cell {
          display: flex;
          justify-content: space-between;
          padding: 0.25rem 0;

          &::before {
            content: attr(data-label);
            font-weight: 600;
            color: var(--grey);
          }
        }
      }
    }
  }


</style>
