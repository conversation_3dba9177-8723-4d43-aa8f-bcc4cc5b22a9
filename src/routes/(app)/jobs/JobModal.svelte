<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Modal from '$lib/components/Modal.svelte';
  import Button from '$lib/components/Button.svelte';
  import CustomerSelect from '$lib/components/CustomerSelect.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import {
    selectedJob,
    jobModalOpen,
    jobModalMode,
    jobTypes,
    jobStore
  } from '$lib/stores/jobStore';
  import { customers, contactStore } from '$lib/stores/customerStore';
  import { activeStaff, staffStore } from '$lib/stores/staffStore';
  import type { Job } from '$lib/api/jobs';
  import { onMount } from 'svelte';

  const dispatch = createEventDispatcher();

  // Load customers and staff when component mounts
  onMount(async () => {
    await Promise.all([
      contactStore.loadContacts(),
      staffStore.loadActiveStaff()
    ]);
  });

  // Form data
  let formData = {
    title: '',
    description: '',
    customerId: '',
    jobTypeId: '',
    scheduledDate: '',
    scheduledTime: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US'
    },
    priority: 'Medium' as 'Low' | 'Medium' | 'High' | 'Urgent',
    estimatedHours: 0,
    customFields: [] as Array<{ key: string; value: string }>,
    assignedStaffIds: [] as string[]
  };

  // Customer search state
  let customerSearch = '';
  let formSubmitted = false;
  let errors: Record<string, string> = {};

  // Reset form when modal opens/closes or selected job changes
  $: if ($jobModalOpen && $selectedJob && $jobModalMode === 'edit') {
    // Populate form with selected job data
    formData = {
      title: $selectedJob.title,
      description: $selectedJob.description,
      customerId: $selectedJob.customerId,
      jobTypeId: $selectedJob.jobType || '',
      scheduledDate: $selectedJob.scheduledDateTime ? $selectedJob.scheduledDateTime.split('T')[0] : '',
      scheduledTime: $selectedJob.scheduledDateTime ? $selectedJob.scheduledDateTime.split('T')[1]?.substring(0, 5) : '',
      address: { ...$selectedJob.jobAddress },
      priority: $selectedJob.priority,
      estimatedHours: Math.round(($selectedJob.estimatedDuration || 0) / 60 * 100) / 100,
      customFields: $selectedJob.customFields.map(cf => ({ key: cf.key, value: cf.value })),
      assignedStaffIds: $selectedJob.assignedStaff.map(staff => staff.staffId)
    };

    // Set customer search to the selected customer's name
    const customer = $customers.find(c => c.id === $selectedJob.customerId);
    customerSearch = customer ? (customer.companyName || customer.fullName) : '';
    formSubmitted = false;
    errors = {};
  } else if ($jobModalOpen && $jobModalMode === 'create') {
    // Reset form for new job
    formData = {
      title: '',
      description: '',
      customerId: '',
      jobTypeId: '',
      scheduledDate: '',
      scheduledTime: '',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'US'
      },
      priority: 'Medium',
      estimatedHours: 0,
      customFields: [],
      assignedStaffIds: []
    };
    customerSearch = '';
    formSubmitted = false;
    errors = {};
  }

  function handleCustomerSelect(customerId: string) {
    formData.customerId = customerId;
    // Clear customer error if it exists
    if (errors.customerId) {
      errors = { ...errors };
      delete errors.customerId;
    }
  }

  async function handleSubmit() {
    formSubmitted = true;
    errors = {};

    try {
      // Validate required fields
      if (!formData.title.trim()) {
        errors.title = 'Job title is required';
      }

      if (!formData.customerId) {
        errors.customerId = 'Customer is required';
      }

      // If there are validation errors, don't submit
      if (Object.keys(errors).length > 0) {
        return;
      }

      // Find customer name for display
      const customer = $customers.find(c => c.id === formData.customerId);

      if (!customer) {
        addToast({ message: 'Selected customer not found', type: 'error' });
        return;
      }

      // Combine date and time
      let scheduledDateTime: string | undefined;
      if (formData.scheduledDate) {
        scheduledDateTime = formData.scheduledTime
          ? `${formData.scheduledDate}T${formData.scheduledTime}:00`
          : `${formData.scheduledDate}T09:00:00`;
      }

      // Convert custom fields to proper format
      const customFields = formData.customFields
        .filter(cf => cf.key.trim() && cf.value.trim())
        .map(cf => ({
          id: generateId(),
          key: cf.key.trim(),
          value: cf.value.trim(),
          type: 'text' as const,
          options: undefined
        }));

      const jobData = {
        title: formData.title.trim(),
        customerId: formData.customerId,
        customerName: customer.fullName,
        jobType: formData.jobTypeId,
        description: formData.description.trim(),
        scheduledDateTime,
        jobAddress: formData.address,
        priority: formData.priority,
        estimatedDuration: formData.estimatedHours * 60, // Convert hours to minutes
        customFields,
        tags: $selectedJob?.tags || [],
        // Default values for new jobs
        status: $selectedJob?.status || { id: '1', name: 'Backlog', color: '#6B7280', order: 1, isCompleted: false },
        assignedStaff: formData.assignedStaffIds.map(staffId => {
          const staffMember = $activeStaff.find(s => s.id === staffId);
          return {
            staffId,
            staffName: staffMember?.fullName || 'Unknown',
            confirmed: false
          };
        }),
        attachments: $selectedJob?.attachments || [],
        notes: $selectedJob?.notes || ''
      };

      // Helper function to generate IDs (should match the one in jobs.ts)
      function generateId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
      }

      if ($jobModalMode === 'edit' && $selectedJob) {
        await jobStore.updateJob($selectedJob.id, jobData);
        addToast({ message: 'Job updated successfully', type: 'success' });
      } else {
        await jobStore.addJob(jobData);
        addToast({ message: 'Job created successfully', type: 'success' });
      }

      jobStore.closeJobModal();
    } catch (error) {
      console.error('Error saving job:', error);
      addToast({ message: 'Failed to save job', type: 'error' });
    }
  }

  function addCustomField() {
    formData.customFields = [...formData.customFields, { key: '', value: '' }];
  }

  function removeCustomField(index: number) {
    formData.customFields = formData.customFields.filter((_, i) => i !== index);
  }

  function handleClose() {
    jobStore.closeJobModal();
  }
</script>

<Modal show={$jobModalOpen} title={$jobModalMode === 'edit' ? 'Edit Job' : 'Create New Job'} on:close={handleClose}>
    <form on:submit|preventDefault={handleSubmit} class="job-form">
      <div class="form-grid">
        <!-- Job Title -->
        <div class="form-group">
          <label for="title">Job Title *</label>
          <input
            id="title"
            type="text"
            bind:value={formData.title}
            placeholder="Enter job title"
            required
          />
        </div>

        <!-- Customer -->
        <div class="form-group">
          <CustomerSelect
            bind:customerId={formData.customerId}
            bind:customerSearch={customerSearch}
            hasError={formSubmitted && !!errors.customerId}
            errorMessage={formSubmitted && errors.customerId ? errors.customerId : ''}
            on:selectcustomer={(event) => handleCustomerSelect(event.detail)}
          />
        </div>

        <!-- Job Type -->
        <div class="form-group">
          <label for="jobType">Job Type</label>
          <select id="jobType" bind:value={formData.jobTypeId}>
            <option value="">Select job type</option>
            {#each $jobTypes as jobType}
              <option value={jobType.id}>{jobType.name}</option>
            {/each}
          </select>
        </div>

        <!-- Priority -->
        <div class="form-group">
          <label for="priority">Priority</label>
          <select id="priority" bind:value={formData.priority}>
            <option value="Low">Low</option>
            <option value="Medium">Medium</option>
            <option value="High">High</option>
            <option value="Urgent">Urgent</option>
          </select>
        </div>

        <!-- Scheduled Date -->
        <div class="form-group">
          <label for="scheduledDate">Scheduled Date</label>
          <input
            id="scheduledDate"
            type="date"
            bind:value={formData.scheduledDate}
          />
        </div>

        <!-- Scheduled Time -->
        <div class="form-group">
          <label for="scheduledTime">Scheduled Time</label>
          <input
            id="scheduledTime"
            type="time"
            bind:value={formData.scheduledTime}
          />
        </div>

        <!-- Estimated Hours -->
        <div class="form-group">
          <label for="estimatedHours">Estimated Hours</label>
          <input
            id="estimatedHours"
            type="number"
            min="0"
            step="0.5"
            bind:value={formData.estimatedHours}
            placeholder="0"
          />
        </div>

        <!-- Assigned Staff -->
        <div class="form-group staff-section">
          <label>Assigned Staff</label>
          <div class="staff-checkboxes">
            {#each $activeStaff as staffMember}
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  bind:group={formData.assignedStaffIds}
                  value={staffMember.id}
                />
                <span class="checkbox-custom"></span>
                {staffMember.fullName} - {staffMember.position}
              </label>
            {/each}
          </div>
        </div>
      </div>

      <!-- Description -->
      <div class="form-group full-width">
        <label for="description">Description</label>
        <textarea
          id="description"
          bind:value={formData.description}
          placeholder="Enter job description"
          rows="4"
        ></textarea>
      </div>

      <!-- Address -->
      <div class="address-section">
        <h3>Job Address</h3>
        <div class="form-grid">
          <div class="form-group">
            <label for="street">Street Address</label>
            <input
              id="street"
              type="text"
              bind:value={formData.address.street}
              placeholder="Enter street address"
            />
          </div>

          <div class="form-group">
            <label for="city">City</label>
            <input
              id="city"
              type="text"
              bind:value={formData.address.city}
              placeholder="Enter city"
            />
          </div>

          <div class="form-group">
            <label for="state">State</label>
            <input
              id="state"
              type="text"
              bind:value={formData.address.state}
              placeholder="Enter state"
            />
          </div>

          <div class="form-group">
            <label for="zipCode">ZIP Code</label>
            <input
              id="zipCode"
              type="text"
              bind:value={formData.address.zipCode}
              placeholder="Enter ZIP code"
            />
          </div>
        </div>
      </div>

      <!-- Custom Fields -->
      <div class="custom-fields-section">
        <div class="section-header">
          <h3>Custom Fields</h3>
          <Button type="button" variant="secondary" size="small" on:click={addCustomField}>
            Add Field
          </Button>
        </div>

        {#each formData.customFields as field, index}
          <div class="custom-field">
            <input
              type="text"
              bind:value={field.key}
              placeholder="Field name"
              class="field-key"
            />
            <input
              type="text"
              bind:value={field.value}
              placeholder="Field value"
              class="field-value"
            />
            <Button
              type="button"
              variant="tertiary"
              size="small"
              on:click={() => removeCustomField(index)}
            >
              Remove
            </Button>
          </div>
        {/each}
      </div>

      <div class="form-actions">
        <Button type="button" variant="secondary" on:click={handleClose}>
          Cancel
        </Button>
        <Button type="submit" variant="primary">
          {$jobModalMode === 'edit' ? 'Update Job' : 'Create Job'}
        </Button>
      </div>
    </form>
</Modal>

<style lang="less">
  .job-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
  }

  .staff-section {
    grid-column: 1 / -1;
  }

  .staff-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border: 1px solid var(--border);
    border-radius: var(--br);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--bg);
      border-color: var(--primary);
    }

    input[type="checkbox"] {
      display: none;
    }

    .checkbox-custom {
      width: 18px;
      height: 18px;
      border: 2px solid var(--border);
      border-radius: 3px;
      position: relative;
      transition: all 0.2s ease;

      &::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 6px;
        width: 4px;
        height: 8px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        opacity: 0;
        transition: opacity 0.2s ease;
      }
    }

    input[type="checkbox"]:checked + .checkbox-custom {
      background: var(--primary);
      border-color: var(--primary);

      &::after {
        opacity: 1;
      }
    }
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    &.full-width {
      grid-column: 1 / -1;
    }

    label {
      font-weight: 500;
      color: var(--black);
    }

    input, select, textarea {
      padding: 0.75rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
      font-size: 0.9rem;

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px var(--primary-fade);
      }
    }

    textarea {
      resize: vertical;
      min-height: 100px;
    }

    select[multiple] {
      min-height: 120px;
    }

    .help-text {
      font-size: 0.8rem;
      color: var(--grey);
      margin-top: 0.25rem;
    }
  }

  .address-section, .custom-fields-section {
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1rem;

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1rem;
      color: var(--black);
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      margin: 0;
    }
  }

  .custom-field {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    margin-bottom: 0.5rem;

    .field-key, .field-value {
      flex: 1;
      padding: 0.5rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border);
  }
</style>