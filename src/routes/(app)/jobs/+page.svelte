<script lang="ts">
  import { onMount } from 'svelte';
  import { dndzone } from 'svelte-dnd-action';
  import { flip } from 'svelte/animate';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import Modal from '$lib/components/Modal.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import JobCard from './JobCard.svelte';
  import JobModal from './JobModal.svelte';
  import {
    jobs,
    jobStatuses,
    jobsByStatus,
    jobsLoading,
    jobStatusesLoading,
    jobModalOpen,
    jobStore
  } from '$lib/stores/jobStore';
  import type { Job } from '$lib/api/jobs';
  import PipelineDialog from './PipelineDialog.svelte';

  const flipDurationMs = 200;

  // Pipeline management
  let selectedPipeline = 'default';
  let showPipelineDialog = false;
  let editingPipeline: { id: string; name: string; stages: Array<{ id: string; name: string }> } | null = null;

  // Load data on mount
  onMount(async () => {
    await Promise.all([
      jobStore.loadJobs(),
      jobStore.loadJobStatuses(),
      jobStore.loadJobTypes()
    ]);
  });

  // Local state for drag and drop to avoid conflicts with store updates
  let localJobsByStatus: Record<string, Job[]> = {};

  // Sync local state with store when jobs or statuses change
  $: {
    localJobsByStatus = { ...$jobsByStatus };
  }

  // Handle drag and drop for each column
  function handleDndConsider(statusId: string, e: CustomEvent<{items: Job[]}>) {
    // Update the local state for smooth drag experience
    localJobsByStatus = { ...localJobsByStatus };
    localJobsByStatus[statusId] = e.detail.items;
  }

  async function handleDndFinalize(statusId: string, e: CustomEvent<{items: Job[]}>) {
    const newItems = e.detail.items;

    // Find jobs that need status updates (jobs that are now in a different column than their current status)
    for (const job of newItems) {
      if (job.status.id !== statusId) {
        try {
          await jobStore.updateJobStatus(job.id, statusId);
          addToast({ message: `Job "${job.title}" moved to ${$jobStatuses.find(s => s.id === statusId)?.name || 'new status'}`, type: 'success' });
        } catch (error) {
          console.error('Error updating job status:', error);
          addToast({ message: 'Failed to move job', type: 'error' });
          // Reload jobs to reset the UI state
          await jobStore.loadJobs();
        }
      }
    }
  }

  function openJobModal() {
    jobStore.openJobModal('create');
  }

  function openEditJobModal(job: Job) {
    jobStore.openJobModal('edit', job);
  }

  function openPipelineDialog() {
    editingPipeline = null;
    showPipelineDialog = true;
  }

  function openEditPipelineDialog() {
    // For now, we'll edit the current pipeline (job statuses)
    editingPipeline = {
      id: 'default',
      name: 'Default Pipeline',
      stages: $jobStatuses.map(status => ({
        id: status.id,
        name: status.name
      }))
    };
    showPipelineDialog = true;
  }

  function closePipelineDialog() {
    showPipelineDialog = false;
    editingPipeline = null;
  }

  async function handlePipelineSave(event: CustomEvent<{id: string; name: string; stages: Array<{id: string; name: string}>}>) {
    const pipelineData = event.detail;

    try {
      // For now, we'll just show a success message since we don't have a backend pipeline API yet
      // In the future, this would save to the backend and update the job statuses
      addToast({
        message: `Pipeline "${pipelineData.name}" saved successfully`,
        type: 'success'
      });

      // Close the dialog
      closePipelineDialog();
    } catch (error) {
      console.error('Error saving pipeline:', error);
      addToast({
        message: 'Failed to save pipeline',
        type: 'error'
      });
    }
  }
</script>

<svelte:head>
  <title>Jobs</title>
</svelte:head>

<div class="container">
  <PageHeader title="Jobs">
    <svelte:fragment slot="actions">
      <Button on:click={openJobModal} variant="primary" type="button">Add Job</Button>
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if $jobsLoading || $jobStatusesLoading}
      <div class="loading-container">
        <LoadingSpinner />
        <p>Loading jobs...</p>
      </div>
    {:else}
      <!-- Pipeline Controls -->
      <div class="pipeline-controls">
        <div class="pipeline-selector">
          <label for="pipeline-select">Pipeline:</label>
          <select id="pipeline-select" bind:value={selectedPipeline}>
            <option value="default">Default Pipeline</option>
          </select>
        </div>

        <div class="pipeline-actions">
          <Button variant="secondary" size="small" on:click={openEditPipelineDialog}>
            Edit Pipeline
          </Button>
          <Button variant="secondary" size="small" on:click={openPipelineDialog}>
            New Pipeline
          </Button>
        </div>
      </div>

      <div class="kanban-board">
        {#each $jobStatuses as status (status.id)}
          {@const statusJobs = localJobsByStatus[status.id] || []}
          <div class="kanban-column">
            <div class="kanban-column-header">
              <h3>{status.name}</h3>
              <span class="job-count">{statusJobs.length}</span>
            </div>

            <div class="kanban-column-content"
              use:dndzone={{
                items: statusJobs,
                type: 'job-cards',
                flipDurationMs: 200,
                morphDisabled: false,
                dropFromOthersDisabled: false,
                dragDisabled: false,
                dropTargetStyle: {
                  outline: '2px dashed var(--primary)',
                  outlineOffset: '-4px',
                  backgroundColor: 'var(--secondary-fade)'
                }
              }}
              on:consider={(e) => handleDndConsider(status.id, e)}
              on:finalize={(e) => handleDndFinalize(status.id, e)}>
              {#each statusJobs as job (job.id)}
                <div animate:flip={{duration: 200}}>
                  <JobCard {job} on:edit={() => openEditJobModal(job)} />
                </div>
              {/each}

              {#if statusJobs.length === 0}
                <div class="empty-column-message">
                  No jobs in this stage
                </div>
              {/if}
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </main>
</div>

{#if $jobModalOpen}
  <JobModal />
{/if}

{#if showPipelineDialog}
  <Modal bind:show={showPipelineDialog} title={editingPipeline ? 'Edit Pipeline' : 'Create Pipeline'}>
    <PipelineDialog
      pipeline={editingPipeline}
      on:save={handlePipelineSave}
      on:cancel={closePipelineDialog}
    />
  </Modal>
{/if}

<style lang="less">
  main {
    padding: 1rem 2rem;
  }

  .pipeline-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    .pipeline-selector {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      label {
        font-weight: 500;
      }

      select {
        padding: 0.5rem;
        border-radius: var(--br);
        border: 1px solid var(--border);
        min-width: 200px;
      }
    }

    .pipeline-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .kanban-board {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    padding-bottom: 1rem;
    min-height: calc(100vh - 250px);
  }

  :global(:root) {
    --primary-rgb: 55, 48, 163; /* RGB value for #3730a3 */
  }

  .kanban-column {
    flex: 0 0 300px;
    background: var(--bg);
    border-radius: var(--br);
    border: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    max-height: 100%;

    .kanban-column-header {
      padding: 1rem;
      border-bottom: 1px solid var(--border);
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 1rem;
      }

      .job-count {
        background: var(--primary);
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
      }
    }

    .kanban-column-content {
      padding: 1rem;
      flex: 1;
      overflow-y: auto;
      min-height: 200px;
      position: relative;
    }

    .empty-column-message {
      color: var(--grey);
      text-align: center;
      padding: 2rem 0;
      font-size: 0.9rem;
    }

    /* Simplified styles */
  }
</style>
