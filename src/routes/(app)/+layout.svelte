<script>
  import Sidebar from '$lib/components/Sidebar.svelte';
  import Toasts from '$lib/components/Toasts.svelte';
</script>

<div class="app-layout">
    
        <Sidebar />

    <slot />

    <Toasts />
</div>

<style lang="less">
  :global {
    body {
      padding: 0px;
      margin: 0px;
      font-family: 'IBM Plex Sans', sans-serif;
      background: var(--bg);
    }
    .app-layout {
      display: grid;
      grid-template-columns: 200px 1fr;
    }

    main {
      padding: 2rem;
    }

    .container {
      overflow: auto;
      height: 100vh;
    }

    .form-group {
    flex: 1;
    margin-bottom: 15px;

    label {
      display: block;
      margin-bottom: 10px;
      font-weight: 500;
      font-size: 14px;
      color: var(--grey);
    }

    input, select, textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid var(--border);
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
      font-family: var(--font-family);

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px var(--primary-fade);
      }

      &.error {
        border-color: var(--red);
      }
    }

    textarea {
      resize: vertical;
      min-height: 100px;
    }
  }

  .error-message {
    color: var(--red);
    font-size: 12px;
    margin-top: 4px;
  }



  
  }


</style>