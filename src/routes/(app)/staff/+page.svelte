<script lang="ts">
  import { onMount } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import Modal from '$lib/components/Modal.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { staff, staffStore } from '$lib/stores/staffStore';
  import { 
    createStaff, 
    updateStaff, 
    deleteStaff,
    type Staff,
    type WageInfo,
    type StaffAvailability,
    type TimeOffEntry
  } from '$lib/api/staff';

  let loading = true;
  let showModal = false;
  let editingStaff: Staff | null = null;
  let searchQuery = '';
  let statusFilter = 'All';

  // Form data
  let formData = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    position: '',
    department: '',
    hireDate: new Date().toISOString().split('T')[0],
    isActive: true,
    wageInfo: {
      type: 'Hourly' as WageInfo['type'],
      rate: 0,
      currency: 'USD',
      effectiveDate: new Date().toISOString().split('T')[0]
    },
    skills: [] as string[],
    availability: {
      monday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
      tuesday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
      wednesday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
      thursday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
      friday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
      saturday: { isAvailable: false, startTime: '09:00', endTime: '17:00' },
      sunday: { isAvailable: false, startTime: '09:00', endTime: '17:00' },
      timeOff: [] as TimeOffEntry[]
    }
  };

  let errors: Record<string, string> = {};
  let formSubmitted = false;

  onMount(async () => {
    await loadData();
  });

  async function loadData() {
    loading = true;
    try {
      await staffStore.loadStaff();
    } catch (error) {
      console.error('Error loading staff:', error);
      addToast({ message: 'Failed to load staff', type: 'error' });
    } finally {
      loading = false;
    }
  }

  // Filtered staff based on search and status
  $: filteredStaff = $staff.filter((staffMember: Staff) => {
    const fullName = `${staffMember.firstName} ${staffMember.lastName}`;
    const matchesSearch = fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         staffMember.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         staffMember.position.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'All' || 
                         (statusFilter === 'Active' && staffMember.isActive) ||
                         (statusFilter === 'Inactive' && !staffMember.isActive);
    
    return matchesSearch && matchesStatus;
  });

  function openCreateModal() {
    editingStaff = null;
    resetForm();
    showModal = true;
  }

  function openEditModal(staff: Staff) {
    editingStaff = staff;
    populateForm(staff);
    showModal = true;
  }

  function closeModal() {
    showModal = false;
    editingStaff = null;
    resetForm();
    errors = {};
    formSubmitted = false;
  }

  function resetForm() {
    formData = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      position: '',
      department: '',
      hireDate: new Date().toISOString().split('T')[0],
      isActive: true,
      wageInfo: {
        type: 'Hourly',
        rate: 0,
        currency: 'USD',
        effectiveDate: new Date().toISOString().split('T')[0]
      },
      skills: [],
      availability: {
        monday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
        tuesday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
        wednesday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
        thursday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
        friday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
        saturday: { isAvailable: false, startTime: '09:00', endTime: '17:00' },
        sunday: { isAvailable: false, startTime: '09:00', endTime: '17:00' },
        timeOff: []
      }
    };
  }

  function populateForm(staff: Staff) {
    formData = {
      firstName: staff.firstName,
      lastName: staff.lastName,
      email: staff.email,
      phone: staff.phone,
      position: staff.position,
      department: staff.department || '',
      hireDate: staff.hireDate,
      isActive: staff.isActive,
      wageInfo: {
        type: staff.wageInfo?.type || 'Hourly',
        rate: staff.wageInfo?.rate || 0,
        currency: staff.wageInfo?.currency || 'USD',
        effectiveDate: staff.wageInfo?.effectiveDate || new Date().toISOString().split('T')[0]
      },
      skills: staff.skills || [],
      availability: {
        monday: { 
          isAvailable: staff.availability?.monday?.isAvailable ?? true, 
          startTime: staff.availability?.monday?.startTime || '09:00', 
          endTime: staff.availability?.monday?.endTime || '17:00' 
        },
        tuesday: { 
          isAvailable: staff.availability?.tuesday?.isAvailable ?? true, 
          startTime: staff.availability?.tuesday?.startTime || '09:00', 
          endTime: staff.availability?.tuesday?.endTime || '17:00' 
        },
        wednesday: { 
          isAvailable: staff.availability?.wednesday?.isAvailable ?? true, 
          startTime: staff.availability?.wednesday?.startTime || '09:00', 
          endTime: staff.availability?.wednesday?.endTime || '17:00' 
        },
        thursday: { 
          isAvailable: staff.availability?.thursday?.isAvailable ?? true, 
          startTime: staff.availability?.thursday?.startTime || '09:00', 
          endTime: staff.availability?.thursday?.endTime || '17:00' 
        },
        friday: { 
          isAvailable: staff.availability?.friday?.isAvailable ?? true, 
          startTime: staff.availability?.friday?.startTime || '09:00', 
          endTime: staff.availability?.friday?.endTime || '17:00' 
        },
        saturday: { 
          isAvailable: staff.availability?.saturday?.isAvailable ?? false, 
          startTime: staff.availability?.saturday?.startTime || '09:00', 
          endTime: staff.availability?.saturday?.endTime || '17:00' 
        },
        sunday: { 
          isAvailable: staff.availability?.sunday?.isAvailable ?? false, 
          startTime: staff.availability?.sunday?.startTime || '09:00', 
          endTime: staff.availability?.sunday?.endTime || '17:00' 
        },
        timeOff: staff.availability?.timeOff || []
      }
    };
  }

  function validateForm(): boolean {
    errors = {};

    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.position.trim()) {
      errors.position = 'Position is required';
    }

    if (formData.wageInfo.rate <= 0) {
      errors.wageRate = 'Wage rate must be greater than 0';
    }

    return Object.keys(errors).length === 0;
  }

  async function handleSubmit() {
    formSubmitted = true;

    if (!validateForm()) {
      addToast({
        message: 'Please fix the errors in the form before submitting',
        type: 'error'
      });
      return;
    }

    try {
      const staffData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        position: formData.position,
        department: formData.department,
        hireDate: new Date().toISOString().split('T')[0],
        isActive: formData.isActive,
        wageInfo: {
          ...formData.wageInfo,
          currency: 'USD'
        },
        wageHistory: [],
        skills: [],
        certifications: [],
        availability: {
          ...formData.availability,
          timeOff: []
        },
        notes: ''
      };

      if (editingStaff) {
        await updateStaff(editingStaff.id, staffData);
        addToast({
          message: 'Staff member updated successfully',
          type: 'success'
        });
      } else {
        await createStaff(staffData);
        addToast({
          message: 'Staff member created successfully',
          type: 'success'
        });
      }

      await staffStore.loadStaff();
      closeModal();
    } catch (error) {
      console.error('Error saving staff:', error);
      addToast({
        message: error instanceof Error ? error.message : 'An unknown error occurred',
        type: 'error'
      });
    }
  }

  async function handleDelete(staff: Staff) {
    if (window.confirm(`Are you sure you want to delete ${staff.fullName}?`)) {
      try {
        await deleteStaff(staff.id);
        await staffStore.loadStaff();
        addToast({
          message: 'Staff member deleted successfully',
          type: 'success'
        });
      } catch (error) {
        console.error('Error deleting staff:', error);
        addToast({
          message: 'Failed to delete staff member',
          type: 'error'
        });
      }
    }
  }

  function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  function getActiveStaff(): Staff[] {
    return $staff.filter((staffMember: any) => staffMember.isActive);
  }

  function getInactiveStaff(): Staff[] {
    return $staff.filter((staffMember: any) => !staffMember.isActive);
  }

  function getAverageWage(): number {
    const activeStaff = getActiveStaff();
    if (activeStaff.length === 0) return 0;
    
    const totalWage = activeStaff.reduce((sum, staffMember) => {
      return sum + (staffMember.wageInfo?.rate || 0);
    }, 0);
    
    return totalWage / activeStaff.length;
  }

  const daysOfWeek: Array<{ key: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday', label: string }> = [
    { key: 'monday', label: 'Monday' },
    { key: 'tuesday', label: 'Tuesday' },
    { key: 'wednesday', label: 'Wednesday' },
    { key: 'thursday', label: 'Thursday' },
    { key: 'friday', label: 'Friday' },
    { key: 'saturday', label: 'Saturday' },
    { key: 'sunday', label: 'Sunday' }
  ];
</script>

<svelte:head>
  <title>Staff Management</title>
</svelte:head>

<div class="container">
  <PageHeader title="Staff Management">
    <svelte:fragment slot="actions">
      <Button on:click={openCreateModal} variant="primary" type="button">
        Add Staff Member
      </Button>
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if loading}
      <div class="loading-container">
        <LoadingSpinner />
        <p>Loading staff...</p>
      </div>
    {:else}
      <!-- Stats -->
      <div class="stats">
        <div class="stat-card">
          <h3>Total Staff</h3>
          <p class="stat-number">{$staff.length}</p>
        </div>
        <div class="stat-card">
          <h3>Active Staff</h3>
          <p class="stat-number">{getActiveStaff().length}</p>
        </div>
        <div class="stat-card">
          <h3>Inactive Staff</h3>
          <p class="stat-number">{getInactiveStaff().length}</p>
        </div>
        <div class="stat-card">
          <h3>Average Wage</h3>
          <p class="stat-number">{formatCurrency(getAverageWage())}</p>
        </div>
      </div>

      <!-- Filters and Search -->
      <div class="controls">
        <div class="search-section">
          <input
            type="text"
            placeholder="Search staff..."
            bind:value={searchQuery}
            class="search-input"
          />
        </div>

        <div class="filter-section">
          <label for="status-filter">Status:</label>
          <select id="status-filter" bind:value={statusFilter}>
            <option value="All">All</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
          </select>
        </div>
      </div>

      <!-- Staff List -->
      {#if filteredStaff.length === 0}
        <div class="empty-state">
          <h3>No staff found</h3>
          <p>
            {#if searchQuery || statusFilter !== 'All'}
              Try adjusting your search or filters.
            {:else}
              Get started by adding your first staff member.
            {/if}
          </p>
          {#if !searchQuery && statusFilter === 'All'}
            <Button on:click={openCreateModal} variant="primary">
              Add Staff Member
            </Button>
          {/if}
        </div>
      {:else}
        <div class="staff-grid">
          {#each filteredStaff as staff (staff.id)}
            <div class="staff-card" class:inactive={!staff.isActive}>
              <div class="staff-header">
                <div class="staff-info">
                  <h3 class="staff-name">{staff.fullName}</h3>
                  <p class="staff-position">{staff.position}</p>
                  {#if staff.department}
                    <p class="staff-department">{staff.department}</p>
                  {/if}
                </div>
                <div class="staff-status">
                  <span class="status-badge" class:active={staff.isActive} class:inactive={!staff.isActive}>
                    {staff.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>

              <div class="staff-details">
                <div class="detail-row">
                  <span class="label">Email:</span>
                  <span class="value">{staff.email}</span>
                </div>
                {#if staff.phone}
                  <div class="detail-row">
                    <span class="label">Phone:</span>
                    <span class="value">{staff.phone}</span>
                  </div>
                {/if}
                {#if staff.wageInfo}
                  <div class="detail-row">
                    <span class="label">Wage:</span>
                    <span class="value">
                      {formatCurrency(staff.wageInfo.rate)}
                      {staff.wageInfo.type === 'Hourly' ? '/hour' : 
                       staff.wageInfo.type === 'Salary' ? '/year' : '/job'}
                    </span>
                  </div>
                {/if}
              </div>

              <div class="staff-actions">
                <Button variant="secondary" size="small" on:click={() => openEditModal(staff)}>
                  Edit
                </Button>
                <Button variant="tertiary" size="small" on:click={() => handleDelete(staff)}>
                  Delete
                </Button>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    {/if}
  </main>
</div>

<!-- Staff Modal -->
<Modal bind:show={showModal} title={editingStaff ? 'Edit Staff Member' : 'Add Staff Member'}>
  <form on:submit|preventDefault={handleSubmit} class="staff-form">
    <!-- Basic Information -->
    <div class="form-section">
      <h3>Basic Information</h3>
      
      <div class="form-row">
        <div class="form-group">
          <label for="firstName">First Name *</label>
          <input
            type="text"
            id="firstName"
            bind:value={formData.firstName}
            class:error={formSubmitted && errors.firstName}
            placeholder="Enter first name"
          />
          {#if formSubmitted && errors.firstName}
            <div class="error-message">{errors.firstName}</div>
          {/if}
        </div>

        <div class="form-group">
          <label for="lastName">Last Name *</label>
          <input
            type="text"
            id="lastName"
            bind:value={formData.lastName}
            class:error={formSubmitted && errors.lastName}
            placeholder="Enter last name"
          />
          {#if formSubmitted && errors.lastName}
            <div class="error-message">{errors.lastName}</div>
          {/if}
        </div>

        <div class="form-group">
          <label for="email">Email *</label>
          <input
            type="email"
            id="email"
            bind:value={formData.email}
            class:error={formSubmitted && errors.email}
            placeholder="Enter email address"
          />
          {#if formSubmitted && errors.email}
            <div class="error-message">{errors.email}</div>
          {/if}
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="phone">Phone</label>
          <input
            type="tel"
            id="phone"
            bind:value={formData.phone}
            placeholder="Enter phone number"
          />
        </div>

        <div class="form-group">
          <label for="position">Position *</label>
          <input
            type="text"
            id="position"
            bind:value={formData.position}
            class:error={formSubmitted && errors.position}
            placeholder="Enter position/job title"
          />
          {#if formSubmitted && errors.position}
            <div class="error-message">{errors.position}</div>
          {/if}
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="department">Department</label>
          <input
            type="text"
            id="department"
            bind:value={formData.department}
            placeholder="Enter department"
          />
        </div>

        <div class="form-group">
          <label for="isActive">Status</label>
          <select id="isActive" bind:value={formData.isActive}>
            <option value={true}>Active</option>
            <option value={false}>Inactive</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Wage Information -->
    <div class="form-section">
      <h3>Wage Information</h3>
      
      <div class="form-row">
        <div class="form-group">
          <label for="wageType">Wage Type</label>
          <select id="wageType" bind:value={formData.wageInfo.type}>
            <option value="Hourly">Hourly</option>
            <option value="Salary">Salary</option>
            <option value="Per Job">Per Job</option>
          </select>
        </div>

        <div class="form-group">
          <label for="wageRate">Rate *</label>
          <input
            type="number"
            id="wageRate"
            bind:value={formData.wageInfo.rate}
            min="0"
            step="0.01"
            class:error={formSubmitted && errors.wageRate}
            placeholder="Enter wage rate"
          />
          {#if formSubmitted && errors.wageRate}
            <div class="error-message">{errors.wageRate}</div>
          {/if}
        </div>

        <div class="form-group">
          <label for="effectiveDate">Effective Date</label>
          <input
            type="date"
            id="effectiveDate"
            bind:value={formData.wageInfo.effectiveDate}
          />
        </div>
      </div>
    </div>

    <!-- Availability -->
    <div class="form-section">
      <h3>Availability</h3>
      
      <div class="availability-grid">
        {#each daysOfWeek as day}
          <div class="day-row">
            <div class="day-checkbox">
              <input
                type="checkbox"
                id="available_{day.key}"
                bind:checked={formData.availability[day.key].isAvailable}
              />
              <label for="available_{day.key}">{day.label}</label>
            </div>
            
            {#if formData.availability[day.key].isAvailable}
              <div class="time-inputs">
                <input
                  type="time"
                  bind:value={formData.availability[day.key].startTime}
                  class="time-input"
                />
                <span>to</span>
                <input
                  type="time"
                  bind:value={formData.availability[day.key].endTime}
                  class="time-input"
                />
              </div>
            {/if}
          </div>
        {/each}
      </div>
    </div>

    <div class="form-actions">
      <Button type="button" variant="tertiary" on:click={closeModal}>Cancel</Button>
      <Button type="submit">
        {editingStaff ? 'Update' : 'Create'} Staff Member
      </Button>
    </div>
  </form>
</Modal>

<style lang="less">
  main {
    padding: 1rem 2rem;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .stat-card {
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 1.5rem;
      text-align: center;

      h3 {
        margin: 0 0 0.5rem 0;
        font-size: 0.9rem;
        color: var(--grey);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-number {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
        color: var(--primary);
      }
    }
  }

  .controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;

    .search-section {
      flex: 1;
      max-width: 400px;

      .search-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      label {
        font-weight: 500;
        color: var(--black);
      }

      select {
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        min-width: 120px;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    p {
      margin: 0 0 2rem 0;
      color: var(--grey);
    }
  }

  .staff-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
  }

  .staff-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;
    transition: box-shadow 0.2s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.inactive {
      opacity: 0.7;
      background: var(--bg);
    }

    .staff-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;

      .staff-info {
        .staff-name {
          margin: 0 0 0.25rem 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--black);
        }

        .staff-position {
          margin: 0 0 0.25rem 0;
          color: var(--primary);
          font-weight: 500;
        }

        .staff-department {
          margin: 0;
          font-size: 0.9rem;
          color: var(--grey);
        }
      }

      .staff-status {
        .status-badge {
          font-size: 0.7rem;
          font-weight: 500;
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: var(--br);
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.active {
            background-color: var(--green);
          }

          &.inactive {
            background-color: var(--grey);
          }
        }
      }
    }

    .staff-details {
      margin-bottom: 1rem;

      .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;

        .label {
          color: var(--grey);
          font-weight: 500;
        }

        .value {
          color: var(--black);
        }
      }
    }

    .staff-actions {
      display: flex;
      gap: 0.5rem;
      justify-content: flex-end;
    }
  }

  .staff-form {
    .form-section {
      margin-bottom: 2rem;

      h3 {
        margin: 0 0 1rem 0;
        font-size: 1.1rem;
        color: var(--primary);
        border-bottom: 1px solid var(--border);
        padding-bottom: 0.5rem;
      }
    }

    .form-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 0.5rem;
      }
    }

    .form-group {
      flex: 1;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--black);
      }

      input, select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }

        &.error {
          border-color: var(--red);
        }
      }

      .error-message {
        color: var(--red);
        font-size: 0.8rem;
        margin-top: 0.25rem;
      }
    }

    .availability-grid {
      .day-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        margin-bottom: 0.5rem;

        .day-checkbox {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          min-width: 120px;

          input[type="checkbox"] {
            width: auto;
          }

          label {
            margin: 0;
            font-weight: 500;
          }
        }

        .time-inputs {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          .time-input {
            width: 100px;
            padding: 0.5rem;
            border: 1px solid var(--border);
            border-radius: var(--br);
          }

          span {
            color: var(--grey);
            font-size: 0.9rem;
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid var(--border);
    }
  }

  @media (max-width: 768px) {
    .controls {
      flex-direction: column;
      align-items: stretch;
    }

    .staff-grid {
      grid-template-columns: 1fr;
    }

    .staff-card .staff-header {
      flex-direction: column;
      gap: 1rem;
    }

    .availability-grid .day-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }
</style> 