<script lang="ts">
  import { onMount } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import ContactCard from './ContactCard.svelte';
  import ContactModal from './ContactModal.svelte';
  import { 
    contacts, 
    customers,
    leads,
    contactsLoading,
    selectedContact,
    contactStore 
  } from '$lib/stores/customerStore';
  import type { Contact } from '$lib/api/contacts';

  let searchQuery = '';
  let statusFilter = 'All';
  let showContactModal = false;
  let editingContact: Contact | null = null;

  // Load data on mount
  onMount(async () => {
    await contactStore.loadContacts();
  });

  // Filtered contacts based on search and status
  $: filteredContacts = $contacts.filter(contact => {
    const matchesSearch = contact.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         contact.companyName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         contact.emails.some(email => email.email.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesStatus = statusFilter === 'All' || contact.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  function openAddContactModal() {
    editingContact = null;
    showContactModal = true;
  }

  function openEditContactModal(contact: Contact) {
    editingContact = contact;
    showContactModal = true;
  }

  function closeContactModal() {
    showContactModal = false;
    editingContact = null;
  }

  async function handleContactSave(event: CustomEvent<Contact>) {
    try {
      const contactData = event.detail;
      
      if (editingContact) {
        await contactStore.updateContact(editingContact.id, contactData);
        addToast({ message: 'Contact updated successfully', type: 'success' });
      } else {
        await contactStore.addContact(contactData);
        addToast({ message: 'Contact created successfully', type: 'success' });
      }
      
      closeContactModal();
    } catch (error) {
      console.error('Error saving contact:', error);
      addToast({ message: 'Failed to save contact', type: 'error' });
    }
  }

  async function handleContactDelete(contact: Contact) {
    if (confirm(`Are you sure you want to delete ${contact.fullName}?`)) {
      try {
        await contactStore.deleteContact(contact.id);
        addToast({ message: 'Contact deleted successfully', type: 'success' });
      } catch (error) {
        console.error('Error deleting contact:', error);
        addToast({ message: 'Failed to delete contact', type: 'error' });
      }
    }
  }

  function handleContactSelect(contact: Contact) {
    contactStore.selectContact(contact);
  }
</script>

<svelte:head>
  <title>Contacts</title>
</svelte:head>

<div class="container">
  <PageHeader title="Contacts">
    <svelte:fragment slot="actions">
      <Button on:click={openAddContactModal} variant="primary" type="button">Add Contact</Button>
    </svelte:fragment>
  </PageHeader>

  <main>
    <!-- Filters and Search -->
    <div class="controls">
      <div class="search-section">
        <input
          type="text"
          placeholder="Search contacts..."
          bind:value={searchQuery}
          class="search-input"
        />
      </div>

      <div class="filter-section">
        <label for="status-filter">Status:</label>
        <select id="status-filter" bind:value={statusFilter}>
          <option value="All">All</option>
          <option value="Lead">Leads</option>
          <option value="Customer">Customers</option>
          <option value="Archived">Archived</option>
        </select>
      </div>
    </div>

    <!-- Stats -->
    <div class="stats">
      <div class="stat-card">
        <h3>Total Contacts</h3>
        <p class="stat-number">{$contacts.length}</p>
      </div>
      <div class="stat-card">
        <h3>Customers</h3>
        <p class="stat-number">{$customers.length}</p>
      </div>
      <div class="stat-card">
        <h3>Leads</h3>
        <p class="stat-number">{$leads.length}</p>
      </div>
    </div>

    <!-- Contacts List -->
    {#if $contactsLoading}
      <div class="loading-container">
        <LoadingSpinner />
        <p>Loading contacts...</p>
      </div>
    {:else if filteredContacts.length === 0}
      <div class="empty-state">
        <h3>No contacts found</h3>
        <p>
          {#if searchQuery || statusFilter !== 'All'}
            Try adjusting your search or filters.
          {:else}
            Get started by adding your first contact.
          {/if}
        </p>
        {#if !searchQuery && statusFilter === 'All'}
          <Button on:click={openAddContactModal} variant="primary">Add Contact</Button>
        {/if}
      </div>
    {:else}
      <div class="contacts-grid">
        {#each filteredContacts as contact (contact.id)}
          <ContactCard 
            {contact} 
            on:edit={() => openEditContactModal(contact)}
            on:delete={() => handleContactDelete(contact)}
            on:select={() => handleContactSelect(contact)}
          />
        {/each}
      </div>
    {/if}
  </main>
</div>

{#if showContactModal}
  <ContactModal 
    contact={editingContact}
    on:save={handleContactSave}
    on:cancel={closeContactModal}
  />
{/if}

<style lang="less">
  main {
    padding: 1rem 2rem;
  }

  .controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;

    .search-section {
      flex: 1;
      max-width: 400px;

      .search-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      label {
        font-weight: 500;
        color: var(--black);
      }

      select {
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        min-width: 120px;
      }
    }
  }

  .stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .stat-card {
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 1.5rem;
      text-align: center;

      h3 {
        margin: 0 0 0.5rem 0;
        font-size: 0.9rem;
        color: var(--grey);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-number {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
        color: var(--primary);
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    p {
      margin: 0 0 2rem 0;
      color: var(--grey);
    }
  }

  .contacts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
  }

  @media (max-width: 768px) {
    .controls {
      flex-direction: column;
      align-items: stretch;
    }

    .contacts-grid {
      grid-template-columns: 1fr;
    }
  }
</style> 