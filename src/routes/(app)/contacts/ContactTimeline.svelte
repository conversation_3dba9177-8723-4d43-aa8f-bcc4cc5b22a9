<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { addCommunicationEntry } from '$lib/api/contacts';
  import type { Contact, CommunicationEntry } from '$lib/api/contacts';

  export let contact: Contact;

  const dispatch = createEventDispatcher();

  let showAddEntry = false;
  let newEntry = {
    type: 'Note' as CommunicationEntry['type'],
    title: '',
    description: '',
    date: new Date().toISOString().split('T')[0] // Today's date
  };

  // Sort timeline entries by date (newest first)
  $: sortedTimeline = [...contact.communicationTimeline].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  function resetNewEntry() {
    newEntry = {
      type: 'Note',
      title: '',
      description: '',
      date: new Date().toISOString().split('T')[0]
    };
  }

  async function handleAddEntry() {
    if (!newEntry.title.trim()) {
      addToast({ message: 'Entry title is required', type: 'error' });
      return;
    }

    try {
      const entryData = {
        type: newEntry.type,
        title: newEntry.title.trim(),
        description: newEntry.description.trim() || undefined,
        date: new Date(newEntry.date).toISOString()
      };

      const addedEntry = await addCommunicationEntry(contact.id, entryData);
      
      // Update the contact with the new entry
      const updatedContact = {
        ...contact,
        communicationTimeline: [...contact.communicationTimeline, addedEntry]
      };
      
      dispatch('contactUpdated', updatedContact);
      resetNewEntry();
      showAddEntry = false;
      addToast({ message: 'Timeline entry added successfully', type: 'success' });
    } catch (error) {
      console.error('Error adding timeline entry:', error);
      addToast({ message: 'Failed to add timeline entry', type: 'error' });
    }
  }

  function getEntryIcon(type: CommunicationEntry['type']): string {
    switch (type) {
      case 'Email': return '📧';
      case 'SMS': return '💬';
      case 'WhatsApp': return '📱';
      case 'Note': return '📝';
      case 'Job': return '🔧';
      case 'Invoice': return '💰';
      case 'Quote': return '📋';
      case 'Call': return '📞';
      default: return '📄';
    }
  }

  function getEntryColor(type: CommunicationEntry['type']): string {
    switch (type) {
      case 'Email': return '#3B82F6';
      case 'SMS': return '#10B981';
      case 'WhatsApp': return '#059669';
      case 'Note': return '#6B7280';
      case 'Job': return '#F59E0B';
      case 'Invoice': return '#EF4444';
      case 'Quote': return '#8B5CF6';
      case 'Call': return '#06B6D4';
      default: return '#6B7280';
    }
  }

  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  function formatDateTime(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }
</script>

<div class="timeline-section">
  <div class="section-header">
    <h3>Communication Timeline</h3>
    <div class="header-actions">
      <span class="entry-count">{contact.communicationTimeline.length} entries</span>
      <Button 
        variant="primary" 
        size="small" 
        on:click={() => showAddEntry = !showAddEntry}
      >
        {showAddEntry ? 'Cancel' : 'Add Entry'}
      </Button>
    </div>
  </div>

  <!-- Add New Entry Form -->
  {#if showAddEntry}
    <div class="add-entry-form">
      <div class="form-row">
        <div class="form-group">
          <label for="entryType">Type</label>
          <select id="entryType" bind:value={newEntry.type}>
            <option value="Note">Note</option>
            <option value="Email">Email</option>
            <option value="SMS">SMS</option>
            <option value="WhatsApp">WhatsApp</option>
            <option value="Call">Phone Call</option>
            <option value="Job">Job</option>
            <option value="Invoice">Invoice</option>
            <option value="Quote">Quote</option>
          </select>
        </div>

        <div class="form-group">
          <label for="entryDate">Date</label>
          <input
            id="entryDate"
            type="date"
            bind:value={newEntry.date}
          />
        </div>
      </div>

      <div class="form-group">
        <label for="entryTitle">Title *</label>
        <input
          id="entryTitle"
          type="text"
          bind:value={newEntry.title}
          placeholder="Enter entry title..."
        />
      </div>

      <div class="form-group">
        <label for="entryDescription">Description</label>
        <textarea
          id="entryDescription"
          bind:value={newEntry.description}
          placeholder="Enter additional details..."
          rows="3"
        ></textarea>
      </div>

      <div class="form-actions">
        <Button 
          variant="secondary" 
          size="small" 
          on:click={() => { showAddEntry = false; resetNewEntry(); }}
        >
          Cancel
        </Button>
        <Button 
          variant="primary" 
          size="small" 
          on:click={handleAddEntry}
          disabled={!newEntry.title.trim()}
        >
          Add Entry
        </Button>
      </div>
    </div>
  {/if}

  <!-- Timeline -->
  <div class="timeline">
    {#if sortedTimeline.length === 0}
      <div class="empty-timeline">
        <p>No timeline entries yet. Add your first entry above.</p>
      </div>
    {:else}
      {#each sortedTimeline as entry (entry.id)}
        <div class="timeline-item">
          <div class="timeline-marker" style="background-color: {getEntryColor(entry.type)}">
            <span class="timeline-icon">{getEntryIcon(entry.type)}</span>
          </div>
          
          <div class="timeline-content">
            <div class="timeline-header">
              <div class="timeline-info">
                <h4 class="timeline-title">{entry.title}</h4>
                <div class="timeline-meta">
                  <span class="timeline-type" style="color: {getEntryColor(entry.type)}">
                    {entry.type}
                  </span>
                  <span class="timeline-date" title={formatDateTime(entry.date)}>
                    {formatDate(entry.date)}
                  </span>
                </div>
              </div>
            </div>
            
            {#if entry.description}
              <div class="timeline-description">
                <p>{entry.description}</p>
              </div>
            {/if}

            {#if entry.relatedId}
              <div class="timeline-related">
                <span class="related-label">Related ID:</span>
                <span class="related-id">{entry.relatedId}</span>
              </div>
            {/if}
          </div>
        </div>
      {/each}
    {/if}
  </div>
</div>

<style lang="less">
  .timeline-section {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h3 {
      margin: 0;
      font-size: 1.1rem;
      color: var(--black);
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 1rem;

      .entry-count {
        font-size: 0.9rem;
        color: var(--grey);
        background: var(--bg);
        padding: 0.25rem 0.5rem;
        border-radius: var(--br);
      }
    }
  }

  .add-entry-form {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg);
    border-radius: var(--br);
    border: 1px solid var(--border);

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      label {
        font-weight: 500;
        color: var(--black);
        font-size: 0.9rem;
      }

      input, select, textarea {
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }

      textarea {
        resize: vertical;
        font-family: inherit;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;
      margin-top: 1rem;
    }
  }

  .timeline {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 1rem;
      top: 0;
      bottom: 0;
      width: 2px;
      background: var(--border);
    }
  }

  .empty-timeline {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--grey);

    p {
      margin: 0;
      font-style: italic;
    }
  }

  .timeline-item {
    position: relative;
    padding-left: 3rem;
    padding-bottom: 2rem;

    &:last-child {
      padding-bottom: 0;
    }
  }

  .timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .timeline-icon {
      font-size: 0.8rem;
    }
  }

  .timeline-content {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }

  .timeline-header {
    margin-bottom: 0.75rem;

    .timeline-info {
      .timeline-title {
        margin: 0 0 0.5rem 0;
        font-size: 1rem;
        color: var(--black);
        font-weight: 600;
      }

      .timeline-meta {
        display: flex;
        gap: 1rem;
        align-items: center;
        font-size: 0.8rem;

        .timeline-type {
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .timeline-date {
          color: var(--grey);
        }
      }
    }
  }

  .timeline-description {
    margin-bottom: 0.75rem;

    p {
      margin: 0;
      line-height: 1.5;
      color: var(--black);
    }
  }

  .timeline-related {
    padding: 0.5rem;
    background: var(--bg);
    border-radius: var(--br);
    font-size: 0.8rem;

    .related-label {
      color: var(--grey);
      font-weight: 500;
    }

    .related-id {
      color: var(--primary);
      font-family: monospace;
    }
  }

  @media (max-width: 768px) {
    .add-entry-form .form-row {
      grid-template-columns: 1fr;
    }
  }
</style> 