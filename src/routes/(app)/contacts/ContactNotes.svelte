<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { addContactNote, updateContactNote, deleteContactNote } from '$lib/api/contacts';
  import type { Contact, ContactNote } from '$lib/api/contacts';

  export let contact: Contact;

  const dispatch = createEventDispatcher();

  let newNoteContent = '';
  let editingNote: ContactNote | null = null;
  let editingContent = '';

  // Sort notes by creation date (newest first)
  $: sortedNotes = [...contact.notes].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  async function handleAddNote() {
    if (!newNoteContent.trim()) {
      addToast({ message: 'Note content cannot be empty', type: 'error' });
      return;
    }

    try {
      const newNote = await addContactNote(contact.id, newNoteContent.trim());
      
      // Update the contact with the new note
      const updatedContact = {
        ...contact,
        notes: [...contact.notes, newNote]
      };
      
      dispatch('contactUpdated', updatedContact);
      newNoteContent = '';
      addToast({ message: 'Note added successfully', type: 'success' });
    } catch (error) {
      console.error('Error adding note:', error);
      addToast({ message: 'Failed to add note', type: 'error' });
    }
  }

  function startEditNote(note: ContactNote) {
    editingNote = note;
    editingContent = note.content;
  }

  function cancelEditNote() {
    editingNote = null;
    editingContent = '';
  }

  async function handleUpdateNote() {
    if (!editingNote || !editingContent.trim()) {
      addToast({ message: 'Note content cannot be empty', type: 'error' });
      return;
    }

    try {
      const updatedNote = await updateContactNote(contact.id, editingNote!.id, editingContent.trim());
      
      // Update the contact with the updated note
      const updatedContact = {
        ...contact,
        notes: contact.notes.map(note => 
          note.id === editingNote!.id ? updatedNote : note
        )
      };
      
      dispatch('contactUpdated', updatedContact);
      editingNote = null;
      editingContent = '';
      addToast({ message: 'Note updated successfully', type: 'success' });
    } catch (error) {
      console.error('Error updating note:', error);
      addToast({ message: 'Failed to update note', type: 'error' });
    }
  }

  async function handleDeleteNote(note: ContactNote) {
    if (!confirm('Are you sure you want to delete this note?')) {
      return;
    }

    try {
      await deleteContactNote(contact.id, note.id);
      
      // Update the contact without the deleted note
      const updatedContact = {
        ...contact,
        notes: contact.notes.filter(n => n.id !== note.id)
      };
      
      dispatch('contactUpdated', updatedContact);
      addToast({ message: 'Note deleted successfully', type: 'success' });
    } catch (error) {
      console.error('Error deleting note:', error);
      addToast({ message: 'Failed to delete note', type: 'error' });
    }
  }

  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }
</script>

<div class="notes-section">
  <div class="section-header">
    <h3>Notes</h3>
    <span class="note-count">{contact.notes.length} notes</span>
  </div>

  <!-- Add New Note -->
  <div class="add-note">
    <textarea
      bind:value={newNoteContent}
      placeholder="Add a new note..."
      rows="3"
      class="note-textarea"
    ></textarea>
    <div class="add-note-actions">
      <Button 
        variant="primary" 
        size="small" 
        on:click={handleAddNote}
        disabled={!newNoteContent.trim()}
      >
        Add Note
      </Button>
    </div>
  </div>

  <!-- Notes List -->
  <div class="notes-list">
    {#if sortedNotes.length === 0}
      <div class="empty-notes">
        <p>No notes yet. Add your first note above.</p>
      </div>
    {:else}
      {#each sortedNotes as note (note.id)}
        <div class="note-item">
          <div class="note-header">
            <span class="note-date">{formatDate(note.createdAt)}</span>
            {#if note.updatedAt !== note.createdAt}
              <span class="note-updated">(edited {formatDate(note.updatedAt)})</span>
            {/if}
            <div class="note-actions">
              <button 
                class="action-btn edit-btn" 
                on:click={() => startEditNote(note)}
                title="Edit note"
              >
                Edit
              </button>
              <button 
                class="action-btn delete-btn" 
                on:click={() => handleDeleteNote(note)}
                title="Delete note"
              >
                Delete
              </button>
            </div>
          </div>

          <div class="note-content">
            {#if editingNote?.id === note.id}
              <div class="edit-note">
                <textarea
                  bind:value={editingContent}
                  rows="3"
                  class="note-textarea"
                ></textarea>
                <div class="edit-actions">
                  <Button 
                    variant="primary" 
                    size="small" 
                    on:click={handleUpdateNote}
                    disabled={!editingContent.trim()}
                  >
                    Save
                  </Button>
                  <Button 
                    variant="secondary" 
                    size="small" 
                    on:click={cancelEditNote}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            {:else}
              <p class="note-text">{note.content}</p>
            {/if}
          </div>
        </div>
      {/each}
    {/if}
  </div>
</div>

<style lang="less">
  .notes-section {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h3 {
      margin: 0;
      font-size: 1.1rem;
      color: var(--black);
    }

    .note-count {
      font-size: 0.9rem;
      color: var(--grey);
      background: var(--bg);
      padding: 0.25rem 0.5rem;
      border-radius: var(--br);
    }
  }

  .add-note {
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg);
    border-radius: var(--br);
    border: 1px solid var(--border);

    .note-textarea {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
      font-family: inherit;
      font-size: 0.9rem;
      resize: vertical;
      margin-bottom: 0.75rem;

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px var(--primary-fade);
      }
    }

    .add-note-actions {
      display: flex;
      justify-content: flex-end;
    }
  }

  .notes-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .empty-notes {
    text-align: center;
    padding: 2rem;
    color: var(--grey);

    p {
      margin: 0;
      font-style: italic;
    }
  }

  .note-item {
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1rem;
    background: white;

    &:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
  }

  .note-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.8rem;

    .note-date {
      color: var(--grey);
      font-weight: 500;
    }

    .note-updated {
      color: var(--grey);
      font-style: italic;
    }

    .note-actions {
      margin-left: auto;
      display: flex;
      gap: 0.5rem;
    }

    .action-btn {
      background: none;
      border: none;
      padding: 0.25rem 0.5rem;
      border-radius: var(--br);
      font-size: 0.8rem;
      cursor: pointer;
      transition: background-color 0.2s;

      &.edit-btn {
        color: var(--primary);

        &:hover {
          background: var(--primary-fade);
        }
      }

      &.delete-btn {
        color: #EF4444;

        &:hover {
          background: rgba(239, 68, 68, 0.1);
        }
      }
    }
  }

  .note-content {
    .note-text {
      margin: 0;
      line-height: 1.5;
      color: var(--black);
      white-space: pre-wrap;
    }

    .edit-note {
      .note-textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-family: inherit;
        font-size: 0.9rem;
        resize: vertical;
        margin-bottom: 0.75rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }

      .edit-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
      }
    }
  }
</style> 