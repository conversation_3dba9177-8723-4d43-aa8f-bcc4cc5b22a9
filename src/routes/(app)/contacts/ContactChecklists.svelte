<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { 
    addContactChecklist, 
    updateContactChecklist, 
    deleteContactChecklist,
    addChecklistItem,
    updateChecklistItem,
    deleteChecklistItem
  } from '$lib/api/contacts';
  import type { Contact, ContactChecklist, ChecklistItem } from '$lib/api/contacts';

  export let contact: Contact;

  const dispatch = createEventDispatcher();

  let newChecklistTitle = '';
  let editingChecklist: ContactChecklist | null = null;
  let editingTitle = '';
  let newItemTexts: Record<string, string> = {};

  // Sort checklists by creation date (newest first)
  $: sortedChecklists = [...contact.checklists].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  async function handleAddChecklist() {
    if (!newChecklistTitle.trim()) {
      addToast({ message: 'Checklist title cannot be empty', type: 'error' });
      return;
    }

    try {
      const newChecklist = await addContactChecklist(contact.id, newChecklistTitle.trim());
      
      // Update the contact with the new checklist
      const updatedContact = {
        ...contact,
        checklists: [...contact.checklists, newChecklist]
      };
      
      dispatch('contactUpdated', updatedContact);
      newChecklistTitle = '';
      addToast({ message: 'Checklist created successfully', type: 'success' });
    } catch (error) {
      console.error('Error adding checklist:', error);
      addToast({ message: 'Failed to create checklist', type: 'error' });
    }
  }

  function startEditChecklist(checklist: ContactChecklist) {
    editingChecklist = checklist;
    editingTitle = checklist.title;
  }

  function cancelEditChecklist() {
    editingChecklist = null;
    editingTitle = '';
  }

  async function handleUpdateChecklist() {
    if (!editingChecklist || !editingTitle.trim()) {
      addToast({ message: 'Checklist title cannot be empty', type: 'error' });
      return;
    }

    try {
      const updatedChecklist = await updateContactChecklist(contact.id, editingChecklist.id, {
        title: editingTitle.trim()
      });
      
      // Update the contact with the updated checklist
      const updatedContact = {
        ...contact,
        checklists: contact.checklists.map(checklist => 
          checklist.id === editingChecklist!.id ? updatedChecklist : checklist
        )
      };
      
      dispatch('contactUpdated', updatedContact);
      editingChecklist = null;
      editingTitle = '';
      addToast({ message: 'Checklist updated successfully', type: 'success' });
    } catch (error) {
      console.error('Error updating checklist:', error);
      addToast({ message: 'Failed to update checklist', type: 'error' });
    }
  }

  async function handleDeleteChecklist(checklist: ContactChecklist) {
    if (!confirm('Are you sure you want to delete this checklist?')) {
      return;
    }

    try {
      await deleteContactChecklist(contact.id, checklist.id);
      
      // Update the contact without the deleted checklist
      const updatedContact = {
        ...contact,
        checklists: contact.checklists.filter(c => c.id !== checklist.id)
      };
      
      dispatch('contactUpdated', updatedContact);
      addToast({ message: 'Checklist deleted successfully', type: 'success' });
    } catch (error) {
      console.error('Error deleting checklist:', error);
      addToast({ message: 'Failed to delete checklist', type: 'error' });
    }
  }

  async function handleAddItem(checklistId: string) {
    const itemText = newItemTexts[checklistId]?.trim();
    if (!itemText) {
      addToast({ message: 'Item text cannot be empty', type: 'error' });
      return;
    }

    try {
      const newItem = await addChecklistItem(contact.id, checklistId, itemText);
      
      // Update the contact with the new item
      const updatedContact = {
        ...contact,
        checklists: contact.checklists.map(checklist => 
          checklist.id === checklistId 
            ? { ...checklist, items: [...checklist.items, newItem] }
            : checklist
        )
      };
      
      dispatch('contactUpdated', updatedContact);
      newItemTexts[checklistId] = '';
      addToast({ message: 'Item added successfully', type: 'success' });
    } catch (error) {
      console.error('Error adding item:', error);
      addToast({ message: 'Failed to add item', type: 'error' });
    }
  }

  async function handleToggleItem(checklistId: string, item: ChecklistItem) {
    try {
      const updatedItem = await updateChecklistItem(contact.id, checklistId, item.id, {
        completed: !item.completed
      });
      
      // Update the contact with the updated item
      const updatedContact = {
        ...contact,
        checklists: contact.checklists.map(checklist => 
          checklist.id === checklistId 
            ? {
                ...checklist,
                items: checklist.items.map(i => i.id === item.id ? updatedItem : i)
              }
            : checklist
        )
      };
      
      dispatch('contactUpdated', updatedContact);
    } catch (error) {
      console.error('Error updating item:', error);
      addToast({ message: 'Failed to update item', type: 'error' });
    }
  }

  async function handleDeleteItem(checklistId: string, itemId: string) {
    if (!confirm('Are you sure you want to delete this item?')) {
      return;
    }

    try {
      await deleteChecklistItem(contact.id, checklistId, itemId);
      
      // Update the contact without the deleted item
      const updatedContact = {
        ...contact,
        checklists: contact.checklists.map(checklist => 
          checklist.id === checklistId 
            ? { ...checklist, items: checklist.items.filter(i => i.id !== itemId) }
            : checklist
        )
      };
      
      dispatch('contactUpdated', updatedContact);
      addToast({ message: 'Item deleted successfully', type: 'success' });
    } catch (error) {
      console.error('Error deleting item:', error);
      addToast({ message: 'Failed to delete item', type: 'error' });
    }
  }

  function getCompletionPercentage(checklist: ContactChecklist): number {
    if (checklist.items.length === 0) return 0;
    const completed = checklist.items.filter(item => item.completed).length;
    return Math.round((completed / checklist.items.length) * 100);
  }

  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
</script>

<div class="checklists-section">
  <div class="section-header">
    <h3>Checklists</h3>
    <span class="checklist-count">{contact.checklists.length} checklists</span>
  </div>

  <!-- Add New Checklist -->
  <div class="add-checklist">
    <input
      type="text"
      bind:value={newChecklistTitle}
      placeholder="Enter checklist title..."
      class="checklist-input"
    />
    <Button 
      variant="primary" 
      size="small" 
      on:click={handleAddChecklist}
      disabled={!newChecklistTitle.trim()}
    >
      Add Checklist
    </Button>
  </div>

  <!-- Checklists List -->
  <div class="checklists-list">
    {#if sortedChecklists.length === 0}
      <div class="empty-checklists">
        <p>No checklists yet. Create your first checklist above.</p>
      </div>
    {:else}
      {#each sortedChecklists as checklist (checklist.id)}
        <div class="checklist-item">
          <div class="checklist-header">
            <div class="checklist-info">
              {#if editingChecklist?.id === checklist.id}
                <div class="edit-title">
                  <input
                    type="text"
                    bind:value={editingTitle}
                    class="title-input"
                  />
                  <div class="edit-actions">
                    <Button 
                      variant="primary" 
                      size="small" 
                      on:click={handleUpdateChecklist}
                      disabled={!editingTitle.trim()}
                    >
                      Save
                    </Button>
                    <Button 
                      variant="secondary" 
                      size="small" 
                      on:click={cancelEditChecklist}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              {:else}
                <h4 class="checklist-title">{checklist.title}</h4>
                <div class="checklist-meta">
                  <span class="created-date">Created {formatDate(checklist.createdAt)}</span>
                  <span class="completion-badge" class:completed={getCompletionPercentage(checklist) === 100}>
                    {getCompletionPercentage(checklist)}% complete
                  </span>
                </div>
              {/if}
            </div>

            {#if editingChecklist?.id !== checklist.id}
              <div class="checklist-actions">
                <button 
                  class="action-btn edit-btn" 
                  on:click={() => startEditChecklist(checklist)}
                  title="Edit checklist"
                >
                  Edit
                </button>
                <button 
                  class="action-btn delete-btn" 
                  on:click={() => handleDeleteChecklist(checklist)}
                  title="Delete checklist"
                >
                  Delete
                </button>
              </div>
            {/if}
          </div>

          <!-- Progress Bar -->
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              style="width: {getCompletionPercentage(checklist)}%"
            ></div>
          </div>

          <!-- Checklist Items -->
          <div class="checklist-items">
                         {#each checklist.items as item (item.id)}
               <div class="checklist-item-row" class:completed={item.completed}>
                 <label class="item-checkbox">
                   <input
                     type="checkbox"
                     checked={item.completed}
                     on:change={() => handleToggleItem(checklist.id, item)}
                   />
                   <span class="item-text" class:completed={item.completed}>
                     {item.text}
                   </span>
                 </label>
                <button 
                  class="delete-item-btn" 
                  on:click={() => handleDeleteItem(checklist.id, item.id)}
                  title="Delete item"
                >
                  ×
                </button>
              </div>
            {/each}

            <!-- Add New Item -->
            <div class="add-item">
              <input
                type="text"
                bind:value={newItemTexts[checklist.id]}
                placeholder="Add new item..."
                class="item-input"
                on:keydown={(e) => e.key === 'Enter' && handleAddItem(checklist.id)}
              />
              <Button 
                variant="secondary" 
                size="small" 
                on:click={() => handleAddItem(checklist.id)}
                disabled={!newItemTexts[checklist.id]?.trim()}
              >
                Add
              </Button>
            </div>
          </div>
        </div>
      {/each}
    {/if}
  </div>
</div>

<style lang="less">
  .checklists-section {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h3 {
      margin: 0;
      font-size: 1.1rem;
      color: var(--black);
    }

    .checklist-count {
      font-size: 0.9rem;
      color: var(--grey);
      background: var(--bg);
      padding: 0.25rem 0.5rem;
      border-radius: var(--br);
    }
  }

  .add-checklist {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg);
    border-radius: var(--br);
    border: 1px solid var(--border);

    .checklist-input {
      flex: 1;
      padding: 0.5rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
      font-size: 0.9rem;

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px var(--primary-fade);
      }
    }
  }

  .checklists-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .empty-checklists {
    text-align: center;
    padding: 2rem;
    color: var(--grey);

    p {
      margin: 0;
      font-style: italic;
    }
  }

  .checklist-item {
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;
    background: white;

    &:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
  }

  .checklist-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    .checklist-info {
      flex: 1;

      .checklist-title {
        margin: 0 0 0.5rem 0;
        font-size: 1rem;
        color: var(--black);
      }

      .checklist-meta {
        display: flex;
        gap: 1rem;
        align-items: center;
        font-size: 0.8rem;

        .created-date {
          color: var(--grey);
        }

        .completion-badge {
          background: #FEF3C7;
          color: #92400E;
          padding: 0.25rem 0.5rem;
          border-radius: var(--br);
          font-weight: 500;

          &.completed {
            background: #D1FAE5;
            color: #065F46;
          }
        }
      }

      .edit-title {
        .title-input {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid var(--border);
          border-radius: var(--br);
          font-size: 1rem;
          margin-bottom: 0.5rem;

          &:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px var(--primary-fade);
          }
        }

        .edit-actions {
          display: flex;
          gap: 0.5rem;
        }
      }
    }

    .checklist-actions {
      display: flex;
      gap: 0.5rem;
    }

    .action-btn {
      background: none;
      border: none;
      padding: 0.25rem 0.5rem;
      border-radius: var(--br);
      font-size: 0.8rem;
      cursor: pointer;
      transition: background-color 0.2s;

      &.edit-btn {
        color: var(--primary);

        &:hover {
          background: var(--primary-fade);
        }
      }

      &.delete-btn {
        color: #EF4444;

        &:hover {
          background: rgba(239, 68, 68, 0.1);
        }
      }
    }
  }

  .progress-bar {
    width: 100%;
    height: 6px;
    background: var(--bg);
    border-radius: 3px;
    margin-bottom: 1rem;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: var(--primary);
      transition: width 0.3s ease;
    }
  }

  .checklist-items {
    .checklist-item-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--bg);

      &:last-child {
        border-bottom: none;
      }

      .item-checkbox {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex: 1;
        cursor: pointer;

        input[type="checkbox"] {
          margin: 0;
        }

        .item-text {
          color: var(--black);
          transition: all 0.2s;

          &.completed {
            text-decoration: line-through;
            color: var(--grey);
          }
        }
      }

      .delete-item-btn {
        background: none;
        border: none;
        color: #EF4444;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: var(--br);
        opacity: 0.6;
        transition: opacity 0.2s, background-color 0.2s;

        &:hover {
          opacity: 1;
          background: rgba(239, 68, 68, 0.1);
        }
      }
    }

    .add-item {
      display: flex;
      gap: 0.5rem;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid var(--border);

      .item-input {
        flex: 1;
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }
  }
</style> 