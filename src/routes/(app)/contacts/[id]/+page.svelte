<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import ContactNotes from '../ContactNotes.svelte';
  import ContactChecklists from '../ContactChecklists.svelte';
  import ContactTimeline from '../ContactTimeline.svelte';
  import ContactModal from '../ContactModal.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { contactStore } from '$lib/stores/customerStore';
  import { getContactById } from '$lib/api/contacts';
  import type { Contact } from '$lib/api/contacts';

  let contact: Contact | null = null;
  let loading = true;
  let showEditModal = false;
  let activeTab = 'overview';

  $: contactId = $page.params.id;

  onMount(async () => {
    await loadContact();
  });

  async function loadContact() {
    if (!contactId) return;
    
    loading = true;
    try {
      contact = await getContactById(contactId);
      if (!contact) {
        addToast({ message: 'Contact not found', type: 'error' });
        goto('/contacts');
      }
    } catch (error) {
      console.error('Error loading contact:', error);
      addToast({ message: 'Failed to load contact', type: 'error' });
      goto('/contacts');
    } finally {
      loading = false;
    }
  }

  function handleEditContact() {
    showEditModal = true;
  }

  async function handleContactSave(event: CustomEvent<Contact>) {
    if (!contact) return;
    
    try {
      const contactData = event.detail;
      await contactStore.updateContact(contact.id, contactData);
      contact = { ...contact, ...contactData };
      showEditModal = false;
      addToast({ message: 'Contact updated successfully', type: 'success' });
    } catch (error) {
      console.error('Error updating contact:', error);
      addToast({ message: 'Failed to update contact', type: 'error' });
    }
  }

  function handleContactUpdated(event: CustomEvent<Contact>) {
    contact = event.detail;
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'Customer': return '#10B981';
      case 'Lead': return '#3B82F6';
      case 'Archived': return '#6B7280';
      default: return '#6B7280';
    }
  }

  function getPrimaryEmail(): string {
    if (!contact) return '';
    const primaryEmail = contact.emails.find(email => email.isPrimary);
    return primaryEmail?.email || contact.emails[0]?.email || 'No email';
  }

  function getPrimaryPhone(): string {
    if (!contact) return '';
    const primaryPhone = contact.phones.find(phone => phone.isPrimary);
    return primaryPhone?.phone || contact.phones[0]?.phone || 'No phone';
  }

  function getPrimaryAddress(): string {
    if (!contact) return '';
    const primaryAddress = contact.addresses.find(addr => addr.isPrimary);
    if (!primaryAddress) return 'No address';
    return `${primaryAddress.street}, ${primaryAddress.city}, ${primaryAddress.state} ${primaryAddress.zipCode}`;
  }
</script>

<svelte:head>
  <title>{contact ? contact.fullName : 'Contact'} - Contacts</title>
</svelte:head>

{#if loading}
  <div class="loading-container">
    <LoadingSpinner />
    <p>Loading contact...</p>
  </div>
{:else if contact}
  <div class="container">
    <PageHeader title={contact.fullName}>
      <svelte:fragment slot="actions">
        <Button variant="secondary" on:click={() => goto('/contacts')}>
          Back to Contacts
        </Button>
        <Button variant="primary" on:click={handleEditContact}>
          Edit Contact
        </Button>
      </svelte:fragment>
    </PageHeader>

    <main>
      <!-- Contact Overview -->
      <div class="contact-overview">
        <div class="contact-header">
          <div class="contact-info">
            <h1 class="contact-name">{contact.fullName}</h1>
            {#if contact.companyName}
              <p class="company-name">{contact.companyName}</p>
            {/if}
            <div class="status-badge" style="background-color: {getStatusColor(contact.status)}">
              {contact.status}
            </div>
          </div>
        </div>

        <div class="contact-details-grid">
          <div class="detail-card">
            <h3>Contact Information</h3>
            <div class="detail-list">
              <div class="detail-item">
                <span class="label">Email:</span>
                <span class="value">{getPrimaryEmail()}</span>
              </div>
              <div class="detail-item">
                <span class="label">Phone:</span>
                <span class="value">{getPrimaryPhone()}</span>
              </div>
              <div class="detail-item">
                <span class="label">Address:</span>
                <span class="value">{getPrimaryAddress()}</span>
              </div>
            </div>
          </div>

          <div class="detail-card">
            <h3>Summary</h3>
            <div class="summary-stats">
              <div class="stat">
                <span class="stat-number">{contact.notes.length}</span>
                <span class="stat-label">Notes</span>
              </div>
              <div class="stat">
                <span class="stat-number">{contact.checklists.length}</span>
                <span class="stat-label">Checklists</span>
              </div>
              <div class="stat">
                <span class="stat-number">{contact.communicationTimeline.length}</span>
                <span class="stat-label">Timeline Entries</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <div class="tabs">
        <button 
          class="tab" 
          class:active={activeTab === 'overview'}
          on:click={() => activeTab = 'overview'}
        >
          Overview
        </button>
        <button 
          class="tab" 
          class:active={activeTab === 'notes'}
          on:click={() => activeTab = 'notes'}
        >
          Notes ({contact.notes.length})
        </button>
        <button 
          class="tab" 
          class:active={activeTab === 'checklists'}
          on:click={() => activeTab = 'checklists'}
        >
          Checklists ({contact.checklists.length})
        </button>
        <button 
          class="tab" 
          class:active={activeTab === 'timeline'}
          on:click={() => activeTab = 'timeline'}
        >
          Timeline ({contact.communicationTimeline.length})
        </button>
      </div>

      <!-- Tab Content -->
      <div class="tab-content">
        {#if activeTab === 'overview'}
          <div class="overview-content">
            <div class="overview-grid">
              <!-- All Contact Details -->
              <div class="detail-section">
                <h3>All Email Addresses</h3>
                {#each contact.emails as email}
                  <div class="contact-item" class:primary={email.isPrimary}>
                    <span class="item-value">{email.email}</span>
                    <span class="item-type">{email.type}</span>
                    {#if email.isPrimary}<span class="primary-badge">Primary</span>{/if}
                  </div>
                {/each}
              </div>

              <div class="detail-section">
                <h3>All Phone Numbers</h3>
                {#each contact.phones as phone}
                  <div class="contact-item" class:primary={phone.isPrimary}>
                    <span class="item-value">{phone.phone}</span>
                    <span class="item-type">{phone.type}</span>
                    {#if phone.isPrimary}<span class="primary-badge">Primary</span>{/if}
                  </div>
                {/each}
              </div>

              <div class="detail-section">
                <h3>All Addresses</h3>
                {#each contact.addresses as address}
                  <div class="contact-item" class:primary={address.isPrimary}>
                    <div class="item-value">
                      {address.street}<br>
                      {address.city}, {address.state} {address.zipCode}
                    </div>
                    <span class="item-type">{address.type}</span>
                    {#if address.isPrimary}<span class="primary-badge">Primary</span>{/if}
                  </div>
                {/each}
              </div>
            </div>
          </div>
        {:else if activeTab === 'notes'}
          <ContactNotes {contact} on:contactUpdated={handleContactUpdated} />
        {:else if activeTab === 'checklists'}
          <ContactChecklists {contact} on:contactUpdated={handleContactUpdated} />
        {:else if activeTab === 'timeline'}
          <ContactTimeline {contact} on:contactUpdated={handleContactUpdated} />
        {/if}
      </div>
    </main>
  </div>

  {#if showEditModal}
    <ContactModal 
      {contact}
      on:save={handleContactSave}
      on:cancel={() => showEditModal = false}
    />
  {/if}
{:else}
  <div class="error-container">
    <h2>Contact Not Found</h2>
    <p>The contact you're looking for doesn't exist.</p>
    <Button variant="primary" on:click={() => goto('/contacts')}>
      Back to Contacts
    </Button>
  </div>
{/if}

<style lang="less">
  main {
    padding: 1rem 2rem;
  }

  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .contact-overview {
    margin-bottom: 2rem;
  }

  .contact-header {
    margin-bottom: 2rem;

    .contact-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex-wrap: wrap;

      .contact-name {
        margin: 0;
        font-size: 2rem;
        color: var(--black);
      }

      .company-name {
        margin: 0;
        font-size: 1.2rem;
        color: var(--grey);
        font-style: italic;
      }

      .status-badge {
        font-size: 0.8rem;
        font-weight: 500;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: var(--br);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }

  .contact-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .detail-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1.1rem;
      color: var(--black);
    }

    .detail-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .detail-item {
        display: flex;
        justify-content: space-between;

        .label {
          font-weight: 500;
          color: var(--grey);
        }

        .value {
          color: var(--black);
          text-align: right;
        }
      }
    }

    .summary-stats {
      display: flex;
      justify-content: space-around;
      gap: 1rem;

      .stat {
        text-align: center;

        .stat-number {
          display: block;
          font-size: 2rem;
          font-weight: 600;
          color: var(--primary);
        }

        .stat-label {
          font-size: 0.9rem;
          color: var(--grey);
        }
      }
    }
  }

  .tabs {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 2rem;

    .tab {
      background: none;
      border: none;
      padding: 1rem 1.5rem;
      cursor: pointer;
      font-size: 0.9rem;
      color: var(--grey);
      border-bottom: 2px solid transparent;
      transition: all 0.2s;

      &:hover {
        color: var(--black);
        background: var(--bg);
      }

      &.active {
        color: var(--primary);
        border-bottom-color: var(--primary);
        background: var(--bg);
      }
    }
  }

  .tab-content {
    min-height: 400px;
  }

  .overview-content {
    .overview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .detail-section {
      background: white;
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 1.5rem;

      h3 {
        margin: 0 0 1rem 0;
        font-size: 1rem;
        color: var(--black);
      }

      .contact-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        background: var(--bg);

        &.primary {
          border-color: var(--primary);
          background: var(--primary-fade);
        }

        .item-value {
          flex: 1;
          color: var(--black);
          line-height: 1.4;
        }

        .item-type {
          font-size: 0.8rem;
          color: var(--grey);
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-left: 1rem;
        }

        .primary-badge {
          font-size: 0.7rem;
          background: var(--primary);
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: var(--br);
          margin-left: 0.5rem;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .contact-header .contact-info {
      flex-direction: column;
      align-items: flex-start;
    }

    .tabs {
      overflow-x: auto;
      white-space: nowrap;
    }

    .overview-content .overview-grid {
      grid-template-columns: 1fr;
    }
  }
</style> 