<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import type { Contact } from '$lib/api/contacts';

  export let contact: Contact;

  const dispatch = createEventDispatcher();

  function handleEdit() {
    dispatch('edit', contact);
  }

  function handleDelete() {
    dispatch('delete', contact);
  }

  function handleSelect() {
    dispatch('select', contact);
  }

  function handleViewDetails() {
    window.location.href = `/contacts/${contact.id}`;
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'Customer': return '#10B981';
      case 'Lead': return '#3B82F6';
      case 'Archived': return '#6B7280';
      default: return '#6B7280';
    }
  }

  function getPrimaryEmail(): string {
    const primaryEmail = contact.emails.find(email => email.isPrimary);
    return primaryEmail?.email || contact.emails[0]?.email || 'No email';
  }

  function getPrimaryPhone(): string {
    const primaryPhone = contact.phones.find(phone => phone.isPrimary);
    return primaryPhone?.phone || contact.phones[0]?.phone || 'No phone';
  }

  function getPrimaryAddress(): string {
    const primaryAddress = contact.addresses.find(addr => addr.isPrimary);
    if (!primaryAddress) return 'No address';
    return `${primaryAddress.city}, ${primaryAddress.state}`;
  }
</script>

<div class="contact-card" on:click={handleSelect} on:keydown role="button" tabindex="0">
  <div class="contact-header">
    <div class="contact-info">
      <h3 class="contact-name">{contact.fullName}</h3>
      {#if contact.companyName}
        <p class="company-name">{contact.companyName}</p>
      {/if}
    </div>
    <div class="status-badge" style="background-color: {getStatusColor(contact.status)}">
      {contact.status}
    </div>
  </div>

  <div class="contact-details">
    <div class="detail-item">
      <span class="label">Email:</span>
      <span class="value">{getPrimaryEmail()}</span>
    </div>
    
    <div class="detail-item">
      <span class="label">Phone:</span>
      <span class="value">{getPrimaryPhone()}</span>
    </div>
    
    <div class="detail-item">
      <span class="label">Location:</span>
      <span class="value">{getPrimaryAddress()}</span>
    </div>
  </div>

  {#if contact.notes.length > 0}
    <div class="notes-preview">
      <span class="label">Latest Note:</span>
      <p class="note-text">{contact.notes[contact.notes.length - 1].content}</p>
    </div>
  {/if}

  <div class="contact-actions">
    <Button 
      variant="primary" 
      size="small" 
      on:click={(e) => { e.stopPropagation(); handleViewDetails(); }}
    >
      View Details
    </Button>
    <Button 
      variant="secondary" 
      size="small" 
      on:click={(e) => { e.stopPropagation(); handleEdit(); }}
    >
      Edit
    </Button>
    <Button 
      variant="tertiary" 
      size="small" 
      on:click={(e) => { e.stopPropagation(); handleDelete(); }}
    >
      Delete
    </Button>
  </div>
</div>

<style lang="less">
  .contact-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;
    cursor: pointer;
    transition: box-shadow 0.2s, transform 0.1s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    &:focus {
      outline: 2px solid var(--primary);
      outline-offset: -2px;
    }
  }

  .contact-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    .contact-info {
      flex: 1;

      .contact-name {
        margin: 0 0 0.25rem 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--black);
      }

      .company-name {
        margin: 0;
        font-size: 0.9rem;
        color: var(--grey);
        font-style: italic;
      }
    }

    .status-badge {
      font-size: 0.7rem;
      font-weight: 500;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: var(--br);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .contact-details {
    margin-bottom: 1rem;

    .detail-item {
      display: flex;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;

      .label {
        font-weight: 500;
        color: var(--grey);
        min-width: 70px;
      }

      .value {
        color: var(--black);
        flex: 1;
      }
    }
  }

  .notes-preview {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--bg);
    border-radius: var(--br);
    border-left: 3px solid var(--primary);

    .label {
      font-size: 0.8rem;
      font-weight: 500;
      color: var(--grey);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .note-text {
      margin: 0.5rem 0 0 0;
      font-size: 0.9rem;
      color: var(--black);
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .contact-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid var(--border);
  }
</style> 