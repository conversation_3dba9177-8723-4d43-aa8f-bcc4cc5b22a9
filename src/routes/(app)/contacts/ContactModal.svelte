<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Modal from '$lib/components/Modal.svelte';
  import Button from '$lib/components/Button.svelte';
  import type { Contact, ContactEmail, ContactPhone, ContactAddress } from '$lib/api/contacts';

  export let contact: Contact | null = null;

  const dispatch = createEventDispatcher();

  // Form data
  let formData = {
    fullName: '',
    companyName: '',
    status: 'Lead' as 'Lead' | 'Customer' | 'Archived',
    emails: [] as ContactEmail[],
    phones: [] as ContactPhone[],
    addresses: [] as ContactAddress[]
  };

  // Initialize form data
  $: if (contact) {
    formData = {
      fullName: contact.fullName,
      companyName: contact.companyName || '',
      status: contact.status,
      emails: [...contact.emails],
      phones: [...contact.phones],
      addresses: [...contact.addresses]
    };
  } else {
    formData = {
      fullName: '',
      companyName: '',
      status: 'Lead',
      emails: [{ id: generateId(), email: '', type: 'Work', isPrimary: true }],
      phones: [{ id: generateId(), phone: '', type: 'Work', isPrimary: true }],
      addresses: [{
        id: generateId(),
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'US',
        type: 'Work',
        isPrimary: true
      }]
    };
  }

  function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  function addEmail() {
    formData.emails = [...formData.emails, {
      id: generateId(),
      email: '',
      type: 'Work',
      isPrimary: false
    }];
  }

  function removeEmail(index: number) {
    if (formData.emails.length > 1) {
      formData.emails = formData.emails.filter((_, i) => i !== index);
      // Ensure at least one email is primary
      if (!formData.emails.some(email => email.isPrimary)) {
        formData.emails[0].isPrimary = true;
      }
    }
  }

  function setPrimaryEmail(index: number) {
    formData.emails = formData.emails.map((email, i) => ({
      ...email,
      isPrimary: i === index
    }));
  }

  function addPhone() {
    formData.phones = [...formData.phones, {
      id: generateId(),
      phone: '',
      type: 'Work',
      isPrimary: false
    }];
  }

  function removePhone(index: number) {
    if (formData.phones.length > 1) {
      formData.phones = formData.phones.filter((_, i) => i !== index);
      // Ensure at least one phone is primary
      if (!formData.phones.some(phone => phone.isPrimary)) {
        formData.phones[0].isPrimary = true;
      }
    }
  }

  function setPrimaryPhone(index: number) {
    formData.phones = formData.phones.map((phone, i) => ({
      ...phone,
      isPrimary: i === index
    }));
  }

  function addAddress() {
    formData.addresses = [...formData.addresses, {
      id: generateId(),
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US',
      type: 'Work',
      isPrimary: false
    }];
  }

  function removeAddress(index: number) {
    if (formData.addresses.length > 1) {
      formData.addresses = formData.addresses.filter((_, i) => i !== index);
      // Ensure at least one address is primary
      if (!formData.addresses.some(addr => addr.isPrimary)) {
        formData.addresses[0].isPrimary = true;
      }
    }
  }

  function setPrimaryAddress(index: number) {
    formData.addresses = formData.addresses.map((address, i) => ({
      ...address,
      isPrimary: i === index
    }));
  }

  function handleSubmit() {
    // Validate required fields
    if (!formData.fullName.trim()) {
      alert('Full name is required');
      return;
    }

    if (!formData.emails.some(email => email.email.trim())) {
      alert('At least one email is required');
      return;
    }

    // Clean up empty entries
    const cleanedData = {
      ...formData,
      fullName: formData.fullName.trim(),
      companyName: formData.companyName.trim(),
      emails: formData.emails.filter(email => email.email.trim()),
      phones: formData.phones.filter(phone => phone.phone.trim()),
      addresses: formData.addresses.filter(addr => addr.street.trim() || addr.city.trim()),
      notes: contact?.notes || [],
      checklists: contact?.checklists || [],
      communicationTimeline: contact?.communicationTimeline || []
    };

    dispatch('save', cleanedData);
  }

  function handleCancel() {
    dispatch('cancel');
  }
</script>

<Modal show={true} title={contact ? 'Edit Contact' : 'Add New Contact'} on:close={handleCancel}>
    <form on:submit|preventDefault={handleSubmit} class="contact-form">
      <!-- Basic Information -->
      <div class="form-section">
        <h3>Basic Information</h3>
        <div class="form-grid">
          <div class="form-group">
            <label for="fullName">Full Name *</label>
            <input
              id="fullName"
              type="text"
              bind:value={formData.fullName}
              placeholder="Enter full name"
              required
            />
          </div>

          <div class="form-group">
            <label for="companyName">Company Name</label>
            <input
              id="companyName"
              type="text"
              bind:value={formData.companyName}
              placeholder="Enter company name (optional)"
            />
          </div>

          <div class="form-group">
            <label for="status">Status</label>
            <select id="status" bind:value={formData.status}>
              <option value="Lead">Lead</option>
              <option value="Customer">Customer</option>
              <option value="Archived">Archived</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Email Addresses -->
      <div class="form-section">
        <div class="section-header">
          <h3>Email Addresses</h3>
          <Button type="button" variant="secondary" size="small" on:click={addEmail}>
            Add Email
          </Button>
        </div>

        {#each formData.emails as email, index}
          <div class="multi-field">
            <input
              type="email"
              bind:value={email.email}
              placeholder="Email address"
              class="field-input"
            />
            <select bind:value={email.type} class="field-type">
              <option value="Work">Work</option>
              <option value="Personal">Personal</option>
              <option value="Other">Other</option>
            </select>
            <label class="primary-checkbox">
              <input
                type="radio"
                name="primaryEmail"
                checked={email.isPrimary}
                on:change={() => setPrimaryEmail(index)}
              />
              Primary
            </label>
            {#if formData.emails.length > 1}
              <Button 
                type="button" 
                variant="tertiary" 
                size="small" 
                on:click={() => removeEmail(index)}
              >
                Remove
              </Button>
            {/if}
          </div>
        {/each}
      </div>

      <!-- Phone Numbers -->
      <div class="form-section">
        <div class="section-header">
          <h3>Phone Numbers</h3>
          <Button type="button" variant="secondary" size="small" on:click={addPhone}>
            Add Phone
          </Button>
        </div>

        {#each formData.phones as phone, index}
          <div class="multi-field">
            <input
              type="tel"
              bind:value={phone.phone}
              placeholder="Phone number"
              class="field-input"
            />
            <select bind:value={phone.type} class="field-type">
              <option value="Work">Work</option>
              <option value="Mobile">Mobile</option>
              <option value="Home">Home</option>
              <option value="Fax">Fax</option>
              <option value="Other">Other</option>
            </select>
            <label class="primary-checkbox">
              <input
                type="radio"
                name="primaryPhone"
                checked={phone.isPrimary}
                on:change={() => setPrimaryPhone(index)}
              />
              Primary
            </label>
            {#if formData.phones.length > 1}
              <Button 
                type="button" 
                variant="tertiary" 
                size="small" 
                on:click={() => removePhone(index)}
              >
                Remove
              </Button>
            {/if}
          </div>
        {/each}
      </div>

      <!-- Addresses -->
      <div class="form-section">
        <div class="section-header">
          <h3>Addresses</h3>
          <Button type="button" variant="secondary" size="small" on:click={addAddress}>
            Add Address
          </Button>
        </div>

        {#each formData.addresses as address, index}
          <div class="address-group">
            <div class="address-header">
              <select bind:value={address.type} class="address-type">
                <option value="Work">Work</option>
                <option value="Home">Home</option>
                <option value="Billing">Billing</option>
                <option value="Shipping">Shipping</option>
                <option value="Other">Other</option>
              </select>
              <label class="primary-checkbox">
                <input
                  type="radio"
                  name="primaryAddress"
                  checked={address.isPrimary}
                  on:change={() => setPrimaryAddress(index)}
                />
                Primary
              </label>
              {#if formData.addresses.length > 1}
                <Button 
                  type="button" 
                  variant="tertiary" 
                  size="small" 
                  on:click={() => removeAddress(index)}
                >
                  Remove
                </Button>
              {/if}
            </div>

            <div class="address-fields">
              <input
                type="text"
                bind:value={address.street}
                placeholder="Street address"
                class="street-input"
              />
              <div class="address-row">
                <input
                  type="text"
                  bind:value={address.city}
                  placeholder="City"
                  class="city-input"
                />
                <input
                  type="text"
                  bind:value={address.state}
                  placeholder="State"
                  class="state-input"
                />
                <input
                  type="text"
                  bind:value={address.zipCode}
                  placeholder="ZIP"
                  class="zip-input"
                />
              </div>
            </div>
          </div>
        {/each}
      </div>

      <div class="form-actions">
        <Button type="button" variant="secondary" on:click={handleCancel}>
          Cancel
        </Button>
        <Button type="submit" variant="primary">
          {contact ? 'Update Contact' : 'Create Contact'}
        </Button>
      </div>
    </form>
</Modal>

<style lang="less">
  .contact-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    max-height: 70vh;
    overflow-y: auto;
  }

  .form-section {
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1rem;
      color: var(--black);
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      margin: 0;
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    label {
      font-weight: 500;
      color: var(--black);
    }

    input, select {
      padding: 0.75rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
      font-size: 0.9rem;

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px var(--primary-fade);
      }
    }
  }

  .multi-field {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    margin-bottom: 0.75rem;

    .field-input {
      flex: 2;
      padding: 0.5rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
    }

    .field-type {
      flex: 1;
      padding: 0.5rem;
      border: 1px solid var(--border);
      border-radius: var(--br);
    }

    .primary-checkbox {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      font-size: 0.9rem;
      white-space: nowrap;
    }
  }

  .address-group {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border: 1px solid var(--border);
    border-radius: var(--br);
    background: var(--bg);

    .address-header {
      display: flex;
      gap: 0.5rem;
      align-items: center;
      margin-bottom: 1rem;

      .address-type {
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
      }

      .primary-checkbox {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.9rem;
      }
    }

    .address-fields {
      .street-input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        margin-bottom: 0.5rem;
      }

      .address-row {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr;
        gap: 0.5rem;

        input {
          padding: 0.5rem;
          border: 1px solid var(--border);
          border-radius: var(--br);
        }
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border);
  }
</style> 