Easy Job Planner - Development Checklist
1. Project Overview & Strategy
[x] Project Goal: Rapidly prototype and implement the front-end for the "Easy Job Planner" application.
[x] Development Strategy:
[x] Local Data Storage: Use the browser's local storage to simulate a persistent state for all data.
[x] Placeholder APIs: Define and use placeholder API functions for all backend interactions, initially interacting with local storage.
[x] Mark each placeholder with a //TODO: API Integration comment.
[x] Add a brief description of the expected request and response format to each placeholder.
2. Contacts (CRM) Functionality
[x] Contact Details Page: Create a page to display and manage contact information.
[x] Capture Full Name.
[x] Capture Company Name (optional).
[x] Capture Email Address (allow multiple).
[x] Capture Phone Number (allow multiple, with type).
[x] Capture Address (allow multiple, with type).
[x] Implement a "Status" field (e.g., Lead, Customer, Archived).
[x] Notes:
[x] Implement the ability to add multiple, time-stamped notes to a contact.
[x] Ensure notes are editable and deletable.
[x] Checklists:
[x] Add a simple checklist feature for contact-related tasks.
[x] Checklist items should have a checkbox to mark as complete.
[x] Communication Timeline:
[x] Create a visual timeline of all interactions.
[x] Include placeholders for Emails, SMS, WhatsApp, Notes, Jobs, and Invoices.
3. Jobs Module
[x] Jobs Screen:
[x] Fix the loading issue for the jobs screen.
[x] Implement the jobs screen as a large modal dialog for creating and editing jobs.
[x] Job Information:
[x] Add a "Job Title" field.
[x] Add a dropdown to select an existing "Customer".
[x] Add a customizable "Job Type" field.
[x] Implement a Kanban-style "Status" pipeline.
[x] Add a rich text "Description" area.
[x] Add "Scheduled Date/Time" pickers.
[x] Add a multi-select dropdown for "Assigned Staff".
[x] Add a "Job Address" field.
[x] Allow for "Custom Fields" (key-value pairs).
[x] Customer-Job Association: Ensure multiple jobs can be associated with a single customer.
4. Invoicing Module
[x] Invoice Creation Screen:
[x] Product/Service Line Items:
[x] Add a dropdown for existing products/services.
[x] Auto-populate description, price, and tax from selected products.
[x] Allow manual entry of new products/services.
[x] Add an "Additional Info" text area for each line item.
[x] Custom Header Fields: Allow adding one-off header fields.
[x] Invoice Templates & Designer:
[x] Create a section for managing invoice templates.
[x] Invoice Designer:
[x] Allow logo uploads.
[x] Allow changing the color scheme.
[x] Allow adding/removing text sections.
[x] Implement the ability to save designs as templates.
5. Quotes/Estimates Module
[x] Quote Templates:
[x] Allow users to create and save flexible quote templates.
[x] Template Sections:
[x] Front Cover/Preamble.
[x] Observational data section.
[x] Recommendations and solutions section.
[x] Line items with descriptions, images, and prices.
[x] Allow for a fluid structure of text and line item sections.
[x] AI-Powered Quote Generation:
[x] Create a structured form for user input.
[x] Integrate with the Gemini Flash API (simulated).
[x] Feed a knowledge base to the AI for accurate quoting.
[x] Process the structured JSON response from the API to populate the quote.
6. Staff Management
[x] Staff Management Area: Create a dedicated section for managing staff.
[x] Staff Profiles:
[x] Store Contact Details (Name, Email, Phone).
[x] Wage Information:
[x] Store wage type (Hourly, Salary, Per Job) and rate.
[x] Track effective dates for wage changes.
[x] Staff Availability Management:
[x] Weekly availability scheduling with time slots.
[x] Active/Inactive status management.
7. Job Calendar
[x] Calendar Views: Implement Daily, Weekly, Monthly, and Yearly views.
[x] Drag-and-Drop: Enable drag-and-drop functionality for rescheduling jobs.
[x] Staff Assignment:
[x] Allow assigning multiple staff members to a job.
[x] Allow for different start times for each assigned staff member on the same job.
8. Invoicing from Jobs/Quotes
[x] Uninvoiced Items: When creating an invoice, display a list of the selected customer's "Uninvoiced" jobs and quotes.
[x] Quick Add: Allow users to click on an uninvoiced item to add it to the invoice.
[x] Multi-Add: Allow adding multiple jobs/quotes to a single invoice.
9. API Placeholder Strategy
[x] API Directory: Organize placeholder functions in a dedicated src/lib/api directory.
[x] Function Naming: Use clear and descriptive names for API functions.
[x] Local Storage Logic: Implement the logic for interacting with local storage within each placeholder function.
[x] Documentation: Add //TODO: API Integration comments and explanations to each placeholder.
10. Initial bug fixes and observations
[ ] On the quotes page, add a set of tabs at the top (like on invoices). add a 'quote designer' button which allows you to create and edit quote templates the same way you can do on the invoices.
[ ] On invoices, the 'create invoice' button doesn't work and says Error: Invoice not found. This should have an example invoice stored in local storage liek everthing else is.
[ ] Add a 'Customers' main menu item which has the new list of customer (Smith Construction, Johnson Enterprises)
[ ] On the jobs screen, 'New Pipeline' opens the dialog but then 'Save pipeline' doesn't work.
[ ] On the jobs screen, clicking on a job should open up the job dialog.